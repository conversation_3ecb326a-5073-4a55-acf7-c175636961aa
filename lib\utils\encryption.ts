/**
 * Digital Code Encryption Utilities
 * Handles secure encryption/decryption of digital codes for gaming products
 */

import CryptoJS from 'crypto-js'

// SECURITY: Use environment variable for production
const SECRET_KEY = process.env.NEXT_PUBLIC_ENCRYPTION_SECRET_KEY || 'alraya-store-dev-key-2024'
const SALT = 'alraya-digital-codes-salt-v2'

/**
 * Generate a secure encryption key using PBKDF2
 */
function generateSecureKey(): string {
  return CryptoJS.PBKDF2(SECRET_KEY, SALT, {
    keySize: 256/32,
    iterations: 10000
  }).toString()
}

/**
 * Encrypt a digital code for secure storage with enhanced security
 */
export function encryptCode(code: string): string {
  try {
    if (!code || code.trim().length === 0) {
      throw new Error('Code cannot be empty')
    }

    const secureKey = generateSecureKey()
    const iv = CryptoJS.lib.WordArray.random(128/8)

    const encrypted = CryptoJS.AES.encrypt(code, secureKey, {
      iv: iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    })

    // Combine IV and encrypted data for secure storage
    const combined = iv.concat(encrypted.ciphertext)
    return combined.toString(CryptoJS.enc.Base64)
  } catch (error) {
    console.error('Code encryption failed:', error)
    throw new Error('Failed to encrypt code')
  }
}

/**
 * Decrypt a digital code for delivery to customer with enhanced security
 */
export function decryptCode(encryptedCode: string): string {
  try {
    if (!encryptedCode || encryptedCode.trim().length === 0) {
      throw new Error('Encrypted code cannot be empty')
    }

    const secureKey = generateSecureKey()
    const combined = CryptoJS.enc.Base64.parse(encryptedCode)

    // Extract IV and ciphertext
    const iv = CryptoJS.lib.WordArray.create(combined.words.slice(0, 4))
    const ciphertext = CryptoJS.lib.WordArray.create(combined.words.slice(4))

    const decrypted = CryptoJS.AES.decrypt(
      CryptoJS.lib.CipherParams.create({ ciphertext: ciphertext }),
      secureKey,
      {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
      }
    )

    const originalCode = decrypted.toString(CryptoJS.enc.Utf8)

    if (!originalCode) {
      throw new Error('Invalid encrypted code or corrupted data')
    }

    return originalCode
  } catch (error) {
    console.error('Code decryption failed:', error)
    throw new Error('Failed to decrypt code')
  }
}

/**
 * Encrypt multiple codes in batch
 */
export function encryptCodes(codes: string[]): string[] {
  return codes.map(code => encryptCode(code))
}

/**
 * Decrypt multiple codes in batch
 */
export function decryptCodes(encryptedCodes: string[]): string[] {
  return encryptedCodes.map(code => decryptCode(code))
}

/**
 * Generate a secure hash for code verification
 */
export function generateCodeHash(code: string): string {
  return CryptoJS.SHA256(code + SECRET_KEY).toString()
}

/**
 * Verify code integrity using hash
 */
export function verifyCodeHash(code: string, hash: string): boolean {
  const expectedHash = generateCodeHash(code)
  return expectedHash === hash
}

/**
 * Mask code for display purposes (show only first/last characters)
 */
export function maskCode(code: string): string {
  if (code.length <= 4) {
    return '*'.repeat(code.length)
  }
  
  const start = code.substring(0, 2)
  const end = code.substring(code.length - 2)
  const middle = '*'.repeat(code.length - 4)
  
  return `${start}${middle}${end}`
}

/**
 * Validate code format (basic validation)
 */
export function validateCodeFormat(code: string): boolean {
  // Basic validation: not empty, reasonable length
  if (!code || code.trim().length === 0) {
    return false
  }
  
  // Check length (most game codes are 8-20 characters)
  if (code.length < 4 || code.length > 50) {
    return false
  }
  
  // Check for valid characters (alphanumeric, hyphens, underscores)
  const validPattern = /^[A-Za-z0-9\-_]+$/
  return validPattern.test(code)
}

/**
 * Generate a random code (for testing purposes)
 */
export function generateRandomCode(length: number = 12): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  
  return result
}

/**
 * Batch generate random codes
 */
export function generateRandomCodes(count: number, length: number = 12): string[] {
  const codes: string[] = []
  const usedCodes = new Set<string>()
  
  while (codes.length < count) {
    const code = generateRandomCode(length)
    if (!usedCodes.has(code)) {
      codes.push(code)
      usedCodes.add(code)
    }
  }
  
  return codes
}

/**
 * Format code for display (add hyphens for readability)
 */
export function formatCodeForDisplay(code: string): string {
  // Add hyphens every 4 characters for better readability
  return code.replace(/(.{4})/g, '$1-').replace(/-$/, '')
}

/**
 * Remove formatting from code (remove hyphens, spaces)
 */
export function cleanCode(code: string): string {
  return code.replace(/[-\s]/g, '').toUpperCase()
}

/**
 * Check if code appears to be encrypted
 */
export function isEncrypted(code: string): boolean {
  // Encrypted codes are typically base64-like strings
  try {
    // Try to decrypt - if it fails, it's likely not encrypted
    decryptCode(code)
    return true
  } catch {
    return false
  }
}

/**
 * Secure code comparison (constant time to prevent timing attacks)
 */
export function secureCompare(a: string, b: string): boolean {
  if (a.length !== b.length) {
    return false
  }
  
  let result = 0
  for (let i = 0; i < a.length; i++) {
    result |= a.charCodeAt(i) ^ b.charCodeAt(i)
  }
  
  return result === 0
}

/**
 * Generate code metadata for tracking
 */
export interface CodeMetadata {
  id: string
  hash: string
  createdAt: Date
  expiresAt?: Date
  codeType: string
  packageId: string
}

export function generateCodeMetadata(
  code: string, 
  packageId: string, 
  codeType: string = 'game_code',
  expiryDays?: number
): CodeMetadata {
  return {
    id: generateId(),
    hash: generateCodeHash(code),
    createdAt: new Date(),
    expiresAt: expiryDays ? new Date(Date.now() + expiryDays * 24 * 60 * 60 * 1000) : undefined,
    codeType,
    packageId
  }
}

/**
 * Generate unique ID
 */
function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2, 9)
}
