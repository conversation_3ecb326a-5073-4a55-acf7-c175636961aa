/**
 * Cross-Tab Synchronization Utility
 * Synchronizes localStorage changes across browser tabs
 */

type SyncEventType = 'products' | 'orders' | 'transactions' | 'wallet' | 'preferences'

interface SyncEvent {
  type: SyncEventType
  action: 'create' | 'update' | 'delete' | 'bulk_update'
  data: any
  timestamp: number
  tabId: string
}

interface SyncListener {
  type: SyncEventType
  callback: (event: SyncEvent) => void
}

class CrossTabSyncManager {
  private listeners: Map<string, SyncListener[]> = new Map()
  private tabId: string
  private isInitialized = false

  constructor() {
    this.tabId = `tab_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    this.initialize()
  }

  private initialize() {
    if (typeof window === 'undefined' || this.isInitialized) return

    // Listen for storage events (fired when localStorage changes in other tabs)
    window.addEventListener('storage', this.handleStorageEvent.bind(this))

    // Listen for custom sync events
    window.addEventListener('sync-event', this.handleCustomSyncEvent.bind(this))

    // Cleanup on page unload
    window.addEventListener('beforeunload', this.cleanup.bind(this))

    this.isInitialized = true
    console.log(`🔄 Cross-tab sync initialized for tab: ${this.tabId}`)
  }

  private handleStorageEvent(event: StorageEvent) {
    if (!event.key || !event.newValue) return

    // Parse sync events from localStorage changes
    if (event.key.startsWith('sync_event_')) {
      try {
        const syncEvent: SyncEvent = JSON.parse(event.newValue)
        
        // Don't process events from the same tab
        if (syncEvent.tabId === this.tabId) return

        this.notifyListeners(syncEvent)
      } catch (error) {
        console.error('Error parsing sync event:', error)
      }
    }

    // Handle direct localStorage changes
    this.handleDirectStorageChange(event)
  }

  private handleDirectStorageChange(event: StorageEvent) {
    const { key, newValue, oldValue } = event

    if (!key || !newValue) return

    let syncType: SyncEventType | null = null
    let action: SyncEvent['action'] = 'update'

    // Determine sync type based on storage key
    if (key === 'alraya_products' || key === 'productTemplates') {
      syncType = 'products'
    } else if (key === 'alraya_orders' || key.startsWith('product_orders')) {
      syncType = 'orders'
    } else if (key.startsWith('transactions_') || key === 'alraya_transactions') {
      syncType = 'transactions'
    } else if (key.startsWith('wallet_')) {
      syncType = 'wallet'
    } else if (key.startsWith('user_preferences_')) {
      syncType = 'preferences'
    }

    if (syncType) {
      // Determine action
      if (!oldValue) {
        action = 'create'
      } else if (!newValue) {
        action = 'delete'
      }

      const syncEvent: SyncEvent = {
        type: syncType,
        action,
        data: { key, newValue, oldValue },
        timestamp: Date.now(),
        tabId: 'external' // Mark as external change
      }

      this.notifyListeners(syncEvent)
    }
  }

  private handleCustomSyncEvent(event: CustomEvent<SyncEvent>) {
    const syncEvent = event.detail
    
    // Don't process events from the same tab
    if (syncEvent.tabId === this.tabId) return

    this.notifyListeners(syncEvent)
  }

  private notifyListeners(syncEvent: SyncEvent) {
    const typeListeners = this.listeners.get(syncEvent.type) || []
    
    typeListeners.forEach(listener => {
      try {
        listener.callback(syncEvent)
      } catch (error) {
        console.error('Error in sync listener:', error)
      }
    })

    // Also notify global listeners
    const globalListeners = this.listeners.get('*') || []
    globalListeners.forEach(listener => {
      try {
        listener.callback(syncEvent)
      } catch (error) {
        console.error('Error in global sync listener:', error)
      }
    })
  }

  /**
   * Add a listener for sync events
   */
  addListener(type: SyncEventType | '*', callback: (event: SyncEvent) => void): () => void {
    const listenerId = `listener_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    if (!this.listeners.has(type)) {
      this.listeners.set(type, [])
    }

    const listener: SyncListener = { type: type as SyncEventType, callback }
    this.listeners.get(type)!.push(listener)

    // Return unsubscribe function
    return () => {
      const listeners = this.listeners.get(type) || []
      const index = listeners.indexOf(listener)
      if (index >= 0) {
        listeners.splice(index, 1)
      }
    }
  }

  /**
   * Broadcast a sync event to other tabs
   */
  broadcast(type: SyncEventType, action: SyncEvent['action'], data: any) {
    const syncEvent: SyncEvent = {
      type,
      action,
      data,
      timestamp: Date.now(),
      tabId: this.tabId
    }

    // Store in localStorage to trigger storage event in other tabs
    const eventKey = `sync_event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    localStorage.setItem(eventKey, JSON.stringify(syncEvent))

    // Clean up the event after a short delay
    setTimeout(() => {
      localStorage.removeItem(eventKey)
    }, 1000)

    // Also dispatch custom event for same-tab listeners
    window.dispatchEvent(new CustomEvent('sync-event', { detail: syncEvent }))
  }

  /**
   * Sync specific data type across tabs
   */
  syncData(type: SyncEventType, data: any, action: SyncEvent['action'] = 'update') {
    this.broadcast(type, action, data)
  }

  /**
   * Request data sync from other tabs
   */
  requestSync(type: SyncEventType) {
    this.broadcast(type, 'bulk_update', { request: 'sync', requesterId: this.tabId })
  }

  private cleanup() {
    this.listeners.clear()
    window.removeEventListener('storage', this.handleStorageEvent.bind(this))
    window.removeEventListener('sync-event', this.handleCustomSyncEvent.bind(this))
    console.log(`🧹 Cross-tab sync cleaned up for tab: ${this.tabId}`)
  }

  /**
   * Get current tab ID
   */
  getTabId(): string {
    return this.tabId
  }

  /**
   * Check if sync is active
   */
  isActive(): boolean {
    return this.isInitialized
  }
}

// Global instance
const syncManager = new CrossTabSyncManager()

/**
 * Hook for using cross-tab sync in React components
 */
export function useCrossTabSync() {
  const addListener = (type: SyncEventType | '*', callback: (event: SyncEvent) => void) => {
    return syncManager.addListener(type, callback)
  }

  const broadcast = (type: SyncEventType, action: SyncEvent['action'], data: any) => {
    syncManager.broadcast(type, action, data)
  }

  const syncData = (type: SyncEventType, data: any, action: SyncEvent['action'] = 'update') => {
    syncManager.syncData(type, data, action)
  }

  const requestSync = (type: SyncEventType) => {
    syncManager.requestSync(type)
  }

  return {
    addListener,
    broadcast,
    syncData,
    requestSync,
    tabId: syncManager.getTabId(),
    isActive: syncManager.isActive()
  }
}

/**
 * Utility functions for common sync operations
 */
export const CrossTabSync = {
  /**
   * Sync product changes
   */
  syncProducts: (products: any[], action: SyncEvent['action'] = 'update') => {
    syncManager.syncData('products', products, action)
  },

  /**
   * Sync order changes
   */
  syncOrders: (orders: any[], action: SyncEvent['action'] = 'update') => {
    syncManager.syncData('orders', orders, action)
  },

  /**
   * Sync wallet changes
   */
  syncWallet: (walletData: any, action: SyncEvent['action'] = 'update') => {
    syncManager.syncData('wallet', walletData, action)
  },

  /**
   * Sync transaction changes
   */
  syncTransactions: (transactions: any[], action: SyncEvent['action'] = 'update') => {
    syncManager.syncData('transactions', transactions, action)
  },

  /**
   * Sync user preferences
   */
  syncPreferences: (preferences: any, action: SyncEvent['action'] = 'update') => {
    syncManager.syncData('preferences', preferences, action)
  },

  /**
   * Listen for all sync events
   */
  onSync: (callback: (event: SyncEvent) => void) => {
    return syncManager.addListener('*', callback)
  },

  /**
   * Listen for specific type sync events
   */
  onTypeSync: (type: SyncEventType, callback: (event: SyncEvent) => void) => {
    return syncManager.addListener(type, callback)
  }
}

/**
 * Enhanced localStorage wrapper with cross-tab sync
 */
export class SyncedStorage {
  static setItem(key: string, value: any, syncType?: SyncEventType) {
    try {
      const serialized = JSON.stringify(value)
      localStorage.setItem(key, serialized)

      // Broadcast sync event if type specified
      if (syncType) {
        syncManager.broadcast(syncType, 'update', { key, value })
      }

      return true
    } catch (error) {
      console.error('Error setting synced storage item:', error)
      return false
    }
  }

  static getItem<T>(key: string, defaultValue?: T): T | null {
    try {
      const item = localStorage.getItem(key)
      return item ? JSON.parse(item) : defaultValue || null
    } catch (error) {
      console.error('Error getting synced storage item:', error)
      return defaultValue || null
    }
  }

  static removeItem(key: string, syncType?: SyncEventType) {
    try {
      localStorage.removeItem(key)

      // Broadcast sync event if type specified
      if (syncType) {
        syncManager.broadcast(syncType, 'delete', { key })
      }

      return true
    } catch (error) {
      console.error('Error removing synced storage item:', error)
      return false
    }
  }

  static clear(syncType?: SyncEventType) {
    try {
      localStorage.clear()

      // Broadcast sync event if type specified
      if (syncType) {
        syncManager.broadcast(syncType, 'delete', { action: 'clear_all' })
      }

      return true
    } catch (error) {
      console.error('Error clearing synced storage:', error)
      return false
    }
  }
}

export default syncManager
