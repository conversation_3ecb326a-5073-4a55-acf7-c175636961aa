/**
 * Authentication and Authorization Middleware
 * Provides security for API routes and admin functions
 */

import { NextRequest, NextResponse } from 'next/server'

export interface AuthUser {
  id: string
  email: string
  role: 'admin' | 'customer' | 'guest'
  permissions: string[]
  isActive: boolean
}

export interface AuthContext {
  user: AuthUser | null
  isAuthenticated: boolean
  hasRole: (role: string) => boolean
  hasPermission: (permission: string) => boolean
}

/**
 * Mock user database (replace with Supabase in production)
 */
const mockUsers: Record<string, AuthUser> = {
  '<EMAIL>': {
    id: 'admin_1',
    email: '<EMAIL>',
    role: 'admin',
    permissions: ['admin:read', 'admin:write', 'products:manage', 'orders:manage', 'users:manage'],
    isActive: true
  },
  '<EMAIL>': {
    id: 'customer_1',
    email: '<EMAIL>',
    role: 'customer',
    permissions: ['orders:read', 'wallet:read', 'wallet:write'],
    isActive: true
  }
}

/**
 * Extract user from request (mock implementation)
 * In production, this would validate JWT tokens or session cookies
 */
function extractUserFromRequest(request: NextRequest): AuthUser | null {
  try {
    // Check for Authorization header
    const authHeader = request.headers.get('authorization')
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7)
      
      // Mock token validation (replace with proper JWT validation)
      if (token === 'admin_token') {
        return mockUsers['<EMAIL>']
      }
      if (token === 'customer_token') {
        return mockUsers['<EMAIL>']
      }
    }

    // Check for session cookie
    const sessionCookie = request.cookies.get('session')
    if (sessionCookie) {
      // Mock session validation (replace with proper session validation)
      const sessionData = JSON.parse(sessionCookie.value || '{}')
      if (sessionData.userId && mockUsers[sessionData.userId]) {
        return mockUsers[sessionData.userId]
      }
    }

    // Check for user ID in query params (for development only)
    const userId = request.nextUrl.searchParams.get('userId')
    if (userId && mockUsers[userId]) {
      return mockUsers[userId]
    }

    return null
  } catch (error) {
    console.error('Error extracting user from request:', error)
    return null
  }
}

/**
 * Create authentication context
 */
function createAuthContext(user: AuthUser | null): AuthContext {
  return {
    user,
    isAuthenticated: user !== null,
    hasRole: (role: string) => user?.role === role || false,
    hasPermission: (permission: string) => user?.permissions.includes(permission) || false
  }
}

/**
 * Authentication middleware for API routes
 */
export function withAuth(
  handler: (request: NextRequest, context: AuthContext) => Promise<NextResponse>,
  options: {
    requireAuth?: boolean
    requiredRole?: string
    requiredPermissions?: string[]
  } = {}
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    const user = extractUserFromRequest(request)
    const authContext = createAuthContext(user)

    // Check authentication requirement
    if (options.requireAuth && !authContext.isAuthenticated) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Check role requirement
    if (options.requiredRole && !authContext.hasRole(options.requiredRole)) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    // Check permission requirements
    if (options.requiredPermissions) {
      const hasAllPermissions = options.requiredPermissions.every(permission =>
        authContext.hasPermission(permission)
      )
      
      if (!hasAllPermissions) {
        return NextResponse.json(
          { error: 'Insufficient permissions' },
          { status: 403 }
        )
      }
    }

    // Check if user is active
    if (authContext.user && !authContext.user.isActive) {
      return NextResponse.json(
        { error: 'Account is inactive' },
        { status: 403 }
      )
    }

    return handler(request, authContext)
  }
}

/**
 * Admin-only middleware
 */
export function withAdminAuth(
  handler: (request: NextRequest, context: AuthContext) => Promise<NextResponse>
) {
  return withAuth(handler, {
    requireAuth: true,
    requiredRole: 'admin'
  })
}

/**
 * Customer authentication middleware
 */
export function withCustomerAuth(
  handler: (request: NextRequest, context: AuthContext) => Promise<NextResponse>
) {
  return withAuth(handler, {
    requireAuth: true,
    requiredRole: 'customer'
  })
}

/**
 * Permission-based middleware
 */
export function withPermissions(
  permissions: string[],
  handler: (request: NextRequest, context: AuthContext) => Promise<NextResponse>
) {
  return withAuth(handler, {
    requireAuth: true,
    requiredPermissions: permissions
  })
}

/**
 * Rate limiting middleware
 */
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

export function withRateLimit(
  maxRequests: number = 100,
  windowMs: number = 60000, // 1 minute
  handler: (request: NextRequest, context: AuthContext) => Promise<NextResponse>
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    const clientId = request.ip || 'unknown'
    const now = Date.now()
    
    // Clean up expired entries
    for (const [key, value] of rateLimitStore.entries()) {
      if (now > value.resetTime) {
        rateLimitStore.delete(key)
      }
    }
    
    // Check current rate limit
    const current = rateLimitStore.get(clientId)
    
    if (current) {
      if (now < current.resetTime) {
        if (current.count >= maxRequests) {
          return NextResponse.json(
            { error: 'Rate limit exceeded' },
            { status: 429 }
          )
        }
        current.count++
      } else {
        // Reset window
        rateLimitStore.set(clientId, { count: 1, resetTime: now + windowMs })
      }
    } else {
      // First request
      rateLimitStore.set(clientId, { count: 1, resetTime: now + windowMs })
    }

    const user = extractUserFromRequest(request)
    const authContext = createAuthContext(user)
    
    return handler(request, authContext)
  }
}

/**
 * Input validation middleware
 */
export function withValidation<T>(
  validator: (data: any) => { isValid: boolean; errors?: string[]; data?: T },
  handler: (request: NextRequest, context: AuthContext, validatedData: T) => Promise<NextResponse>
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    try {
      const body = await request.json()
      const validation = validator(body)
      
      if (!validation.isValid) {
        return NextResponse.json(
          { error: 'Validation failed', details: validation.errors },
          { status: 400 }
        )
      }

      const user = extractUserFromRequest(request)
      const authContext = createAuthContext(user)
      
      return handler(request, authContext, validation.data!)
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid request body' },
        { status: 400 }
      )
    }
  }
}

/**
 * CORS middleware
 */
export function withCORS(
  handler: (request: NextRequest, context: AuthContext) => Promise<NextResponse>,
  options: {
    origin?: string[]
    methods?: string[]
    headers?: string[]
  } = {}
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    const origin = request.headers.get('origin')
    const allowedOrigins = options.origin || ['http://localhost:3000', 'http://localhost:3001']
    const allowedMethods = options.methods || ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS']
    const allowedHeaders = options.headers || ['Content-Type', 'Authorization']

    // Handle preflight requests
    if (request.method === 'OPTIONS') {
      return new NextResponse(null, {
        status: 200,
        headers: {
          'Access-Control-Allow-Origin': origin && allowedOrigins.includes(origin) ? origin : allowedOrigins[0],
          'Access-Control-Allow-Methods': allowedMethods.join(', '),
          'Access-Control-Allow-Headers': allowedHeaders.join(', '),
          'Access-Control-Max-Age': '86400',
        },
      })
    }

    const user = extractUserFromRequest(request)
    const authContext = createAuthContext(user)
    
    const response = await handler(request, authContext)
    
    // Add CORS headers to response
    if (origin && allowedOrigins.includes(origin)) {
      response.headers.set('Access-Control-Allow-Origin', origin)
    }
    
    return response
  }
}

/**
 * Logging middleware
 */
export function withLogging(
  handler: (request: NextRequest, context: AuthContext) => Promise<NextResponse>
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    const start = Date.now()
    const user = extractUserFromRequest(request)
    const authContext = createAuthContext(user)
    
    console.log(`[${new Date().toISOString()}] ${request.method} ${request.url} - User: ${user?.email || 'anonymous'}`)
    
    try {
      const response = await handler(request, authContext)
      const duration = Date.now() - start
      
      console.log(`[${new Date().toISOString()}] ${request.method} ${request.url} - ${response.status} (${duration}ms)`)
      
      return response
    } catch (error) {
      const duration = Date.now() - start
      console.error(`[${new Date().toISOString()}] ${request.method} ${request.url} - ERROR (${duration}ms):`, error)
      
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      )
    }
  }
}

/**
 * Combine multiple middlewares
 */
export function combineMiddlewares(
  ...middlewares: Array<(handler: any) => any>
) {
  return (handler: any) => {
    return middlewares.reduceRight((acc, middleware) => middleware(acc), handler)
  }
}
