/**
 * Comprehensive Error Boundary Component
 * Catches and handles React errors gracefully
 */

"use client"

import React, { Component, ErrorInfo, ReactNode } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON><PERSON><PERSON>gle, RefreshCw, Home, Bug } from 'lucide-react'

interface Props {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: ErrorInfo) => void
  showDetails?: boolean
  level?: 'page' | 'component' | 'critical'
}

interface State {
  hasError: boolean
  error: Error | null
  errorInfo: ErrorInfo | null
  errorId: string
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    }
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo
    })

    // Log error details
    console.error('ErrorBoundary caught an error:', error, errorInfo)

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo)
    }

    // Report error to monitoring service (in production)
    this.reportError(error, errorInfo)
  }

  private reportError = (error: Error, errorInfo: ErrorInfo) => {
    try {
      // In production, send to error monitoring service
      const errorReport = {
        errorId: this.state.errorId,
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        level: this.props.level || 'component'
      }

      // Store locally for now (replace with actual error reporting)
      const existingErrors = JSON.parse(localStorage.getItem('error_reports') || '[]')
      existingErrors.push(errorReport)
      
      // Keep only last 50 errors
      if (existingErrors.length > 50) {
        existingErrors.splice(0, existingErrors.length - 50)
      }
      
      localStorage.setItem('error_reports', JSON.stringify(existingErrors))

      console.log('Error reported:', errorReport)
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError)
    }
  }

  private handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    })
  }

  private handleReload = () => {
    window.location.reload()
  }

  private handleGoHome = () => {
    window.location.href = '/'
  }

  private copyErrorDetails = () => {
    const errorDetails = {
      errorId: this.state.errorId,
      message: this.state.error?.message,
      stack: this.state.error?.stack,
      componentStack: this.state.errorInfo?.componentStack,
      timestamp: new Date().toISOString()
    }

    navigator.clipboard.writeText(JSON.stringify(errorDetails, null, 2))
      .then(() => {
        alert('تم نسخ تفاصيل الخطأ إلى الحافظة')
      })
      .catch(() => {
        console.error('Failed to copy error details')
      })
  }

  render() {
    if (this.state.hasError) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback
      }

      // Render appropriate error UI based on level
      return this.renderErrorUI()
    }

    return this.props.children
  }

  private renderErrorUI() {
    const { level = 'component', showDetails = false } = this.props
    const { error, errorInfo, errorId } = this.state

    if (level === 'critical') {
      return this.renderCriticalError()
    }

    if (level === 'page') {
      return this.renderPageError()
    }

    return this.renderComponentError()
  }

  private renderCriticalError() {
    return (
      <div className="min-h-screen bg-red-50 flex items-center justify-center p-4">
        <Card className="max-w-md w-full border-red-200">
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4">
              <AlertTriangle className="w-6 h-6 text-red-600" />
            </div>
            <CardTitle className="text-red-800">خطأ حرج في النظام</CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <p className="text-red-600">
              حدث خطأ حرج في النظام. يرجى إعادة تحميل الصفحة أو الاتصال بالدعم الفني.
            </p>
            <div className="flex flex-col gap-2">
              <Button onClick={this.handleReload} className="w-full">
                <RefreshCw className="w-4 h-4 mr-2" />
                إعادة تحميل الصفحة
              </Button>
              <Button variant="outline" onClick={this.handleGoHome} className="w-full">
                <Home className="w-4 h-4 mr-2" />
                العودة للرئيسية
              </Button>
            </div>
            <p className="text-xs text-gray-500">
              معرف الخطأ: {this.state.errorId}
            </p>
          </CardContent>
        </Card>
      </div>
    )
  }

  private renderPageError() {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Card className="max-w-lg w-full">
          <CardHeader className="text-center">
            <div className="mx-auto w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mb-4">
              <Bug className="w-8 h-8 text-orange-600" />
            </div>
            <CardTitle className="text-gray-800">حدث خطأ في الصفحة</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-gray-600 text-center">
              عذراً، حدث خطأ غير متوقع. يمكنك المحاولة مرة أخرى أو العودة للصفحة الرئيسية.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-2">
              <Button onClick={this.handleRetry} className="flex-1">
                <RefreshCw className="w-4 h-4 mr-2" />
                المحاولة مرة أخرى
              </Button>
              <Button variant="outline" onClick={this.handleGoHome} className="flex-1">
                <Home className="w-4 h-4 mr-2" />
                الصفحة الرئيسية
              </Button>
            </div>

            {this.props.showDetails && (
              <details className="mt-4">
                <summary className="cursor-pointer text-sm text-gray-500 hover:text-gray-700">
                  عرض تفاصيل الخطأ
                </summary>
                <div className="mt-2 p-3 bg-gray-100 rounded text-xs font-mono text-gray-700 max-h-40 overflow-auto">
                  <p><strong>الرسالة:</strong> {this.state.error?.message}</p>
                  <p><strong>معرف الخطأ:</strong> {this.state.errorId}</p>
                  {this.state.error?.stack && (
                    <div className="mt-2">
                      <strong>التفاصيل:</strong>
                      <pre className="whitespace-pre-wrap">{this.state.error.stack}</pre>
                    </div>
                  )}
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={this.copyErrorDetails}
                  className="mt-2 w-full"
                >
                  نسخ تفاصيل الخطأ
                </Button>
              </details>
            )}
          </CardContent>
        </Card>
      </div>
    )
  }

  private renderComponentError() {
    return (
      <div className="p-4 border border-orange-200 bg-orange-50 rounded-lg">
        <div className="flex items-center space-x-2 space-x-reverse mb-2">
          <AlertTriangle className="w-5 h-5 text-orange-600" />
          <h3 className="text-sm font-medium text-orange-800">
            خطأ في المكون
          </h3>
        </div>
        <p className="text-sm text-orange-700 mb-3">
          حدث خطأ في هذا الجزء من الصفحة. يمكنك المحاولة مرة أخرى.
        </p>
        <Button
          size="sm"
          variant="outline"
          onClick={this.handleRetry}
          className="text-orange-700 border-orange-300 hover:bg-orange-100"
        >
          <RefreshCw className="w-4 h-4 mr-1" />
          إعادة المحاولة
        </Button>
      </div>
    )
  }
}

/**
 * HOC for wrapping components with error boundary
 */
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<Props, 'children'>
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  )

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`
  
  return WrappedComponent
}

/**
 * Hook for error reporting
 */
export function useErrorReporting() {
  const reportError = (error: Error, context?: string) => {
    console.error('Manual error report:', error, context)
    
    try {
      const errorReport = {
        errorId: `manual_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        message: error.message,
        stack: error.stack,
        context: context || 'Manual report',
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href
      }

      const existingErrors = JSON.parse(localStorage.getItem('error_reports') || '[]')
      existingErrors.push(errorReport)
      localStorage.setItem('error_reports', JSON.stringify(existingErrors))
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError)
    }
  }

  return { reportError }
}

/**
 * Get stored error reports
 */
export function getErrorReports(): any[] {
  try {
    return JSON.parse(localStorage.getItem('error_reports') || '[]')
  } catch {
    return []
  }
}

/**
 * Clear error reports
 */
export function clearErrorReports(): void {
  localStorage.removeItem('error_reports')
}
