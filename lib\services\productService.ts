// =====================================================
// PRODUCT MANAGEMENT SERVICE
// =====================================================
// ## TODO: Implement Supabase integration for all functions
// ## DATABASE LATER: Connect to products, packages, custom_fields tables

import { ProductTemplate, ProductPackage, DynamicField, ProductFilters, ProductStats } from '@/lib/types'
import { ProductStorage, initializeDatabase } from '@/lib/storage/localStorage'
import { initializeDefaultTemplates, defaultTemplates } from '@/lib/data/defaultProductTemplates'

// =====================================================
// PRODUCT CRUD OPERATIONS
// =====================================================

/**
 * ## TODO: Implement Supabase product fetching
 * Fetch all products with optional filtering
 */
export async function getProducts(filters?: ProductFilters): Promise<ProductTemplate[]> {
  // Initialize database and ensure sample data exists
  if (typeof window !== 'undefined') {
    initializeDatabase()

    // Check if we need to initialize with sample data
    const existingProducts = ProductStorage.getAll()
    if (existingProducts.length === 0) {
      console.log('🔄 Initializing with sample products...')
      initializeDefaultTemplates()

      // Add default templates to localStorage
      for (const template of defaultTemplates) {
        ProductStorage.create(template)
      }
    }
  }

  // Simulate API delay for realistic UX
  await new Promise(resolve => setTimeout(resolve, 100))

  try {
    // On server-side, return default templates
    if (typeof window === 'undefined') {
      console.log('Server-side: returning default templates')
      return applyFilters(defaultTemplates, filters)
    }

    // On client-side, load from localStorage
    const products = ProductStorage.getActive()
    return applyFilters(products, filters)
  } catch (error) {
    console.error('Error loading products:', error)
    // Fallback to default templates
    return applyFilters(defaultTemplates, filters)
  }
}

/**
 * ## TODO: Implement Supabase product fetching by ID
 * Fetch single product by ID
 */
export async function getProductById(id: string): Promise<ProductTemplate | null> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 100))

  try {
    console.log(`🔍 Looking for product with ID: "${id}"`)

    // On server-side, search in default templates
    if (typeof window === 'undefined') {
      const product = defaultTemplates.find(p => p.id === id && p.isActive)
      return product || null
    }

    // On client-side, search in localStorage
    const product = ProductStorage.getById(id)

    if (product && product.isActive) {
      console.log(`✅ Found product: "${product.name}" (Active: ${product.isActive})`)
      return product
    } else {
      console.log(`❌ Product with ID "${id}" not found or inactive`)
      return null
    }
  } catch (error) {
    console.error('Error in getProductById:', error)
    return null
  }
}

/**
 * ## TODO: Implement Supabase product creation
 * Create new product with packages and fields
 */
export async function createProduct(product: Omit<ProductTemplate, 'id' | 'createdAt' | 'updatedAt'>): Promise<ProductTemplate> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 200))

  try {
    // Use our new localStorage system
    const newProduct = ProductStorage.create(product)
    console.log(`✅ Created product: "${newProduct.name}" with ID: ${newProduct.id}`)
    return newProduct
  } catch (error) {
    console.error('Error creating product:', error)
    throw new Error('Failed to create product')
  }
}

/**
 * ## TODO: Implement Supabase product update
 * Update existing product
 */
export async function updateProduct(id: string, updates: Partial<ProductTemplate>): Promise<ProductTemplate> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 200))

  try {
    const updatedProduct = ProductStorage.update(id, updates)
    if (!updatedProduct) {
      throw new Error(`Product with id ${id} not found`)
    }

    console.log(`✅ Updated product: "${updatedProduct.name}"`)
    return updatedProduct
  } catch (error) {
    console.error('Error updating product:', error)
    throw error
  }
}

/**
 * ## TODO: Implement Supabase product deletion
 * Delete product and related data
 */
export async function deleteProduct(id: string): Promise<boolean> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 200))

  try {
    // Soft delete by setting isActive to false
    const product = ProductStorage.getById(id)
    if (!product) return false

    const updated = ProductStorage.update(id, { isActive: false })
    console.log(`✅ Soft deleted product: "${product.name}"`)
    return !!updated
  } catch (error) {
    console.error('Error deleting product:', error)
    return false
  }
}

/**
 * Hard delete product (completely remove from storage)
 */
export async function hardDeleteProduct(id: string): Promise<boolean> {
  await new Promise(resolve => setTimeout(resolve, 200))

  try {
    const success = ProductStorage.delete(id)
    console.log(`✅ Hard deleted product with ID: ${id}`)
    return success
  } catch (error) {
    console.error('Error hard deleting product:', error)
    return false
  }
}

// =====================================================
// PACKAGE MANAGEMENT
// =====================================================

/**
 * ## TODO: Implement Supabase package operations
 * Get packages for a specific product
 */
export async function getProductPackages(productId: string): Promise<ProductPackage[]> {
  await new Promise(resolve => setTimeout(resolve, 50))

  const product = await getProductById(productId)
  return product?.packages.filter(pkg => pkg.isActive) || []
}

/**
 * Get products by category
 */
export async function getProductsByCategory(category: string): Promise<ProductTemplate[]> {
  await new Promise(resolve => setTimeout(resolve, 100))

  if (typeof window === 'undefined') {
    return defaultTemplates.filter(p =>
      p.isActive && p.category.toLowerCase().includes(category.toLowerCase())
    )
  }

  return ProductStorage.getByCategory(category)
}

/**
 * Search products
 */
export async function searchProducts(query: string): Promise<ProductTemplate[]> {
  await new Promise(resolve => setTimeout(resolve, 100))

  if (typeof window === 'undefined') {
    const searchTerm = query.toLowerCase()
    return defaultTemplates.filter(p =>
      p.isActive && (
        p.name.toLowerCase().includes(searchTerm) ||
        p.description?.toLowerCase().includes(searchTerm) ||
        p.category.toLowerCase().includes(searchTerm) ||
        p.tags?.some(tag => tag.toLowerCase().includes(searchTerm))
      )
    )
  }

  return ProductStorage.search(query)
}

/**
 * Get available digital codes for a package
 */
export async function getAvailableCodes(productId: string, packageId: string) {
  await new Promise(resolve => setTimeout(resolve, 50))
  return ProductStorage.getAvailableCodes(productId, packageId)
}

/**
 * Assign digital code to order
 */
export async function assignDigitalCode(productId: string, packageId: string, orderId: string) {
  await new Promise(resolve => setTimeout(resolve, 100))
  return ProductStorage.assignDigitalCode(productId, packageId, orderId)
}

/**
 * ## TODO: Implement Supabase package creation
 * Add package to product
 */
export async function addPackageToProduct(productId: string, packageData: Omit<ProductPackage, 'id'>): Promise<ProductPackage> {
  // ## TODO: Replace with Supabase insert
  /*
  const { data, error } = await supabase
    .from('product_packages')
    .insert({
      product_id: productId,
      name: packageData.name,
      // ... other fields
    })
    .select()
    .single()
  
  if (error) throw error
  return transformPackageFromDB(data)
  */
  
  const newPackage: ProductPackage = {
    ...packageData,
    id: generateId()
  }
  
  const product = await getProductById(productId)
  if (!product) throw new Error('Product not found')
  
  product.packages.push(newPackage)
  await updateProduct(productId, { packages: product.packages })
  
  return newPackage
}

// =====================================================
// STATISTICS AND ANALYTICS
// =====================================================

/**
 * ## TODO: Implement Supabase analytics queries
 * Get product statistics for admin dashboard
 */
export async function getProductStats(): Promise<ProductStats> {
  // ## TODO: Replace with Supabase aggregation queries
  /*
  const [
    totalProducts,
    activeProducts,
    digitalProducts,
    totalPackages,
    totalOrders,
    popularCategories
  ] = await Promise.all([
    supabase.from('products').select('id', { count: 'exact' }),
    supabase.from('products').select('id', { count: 'exact' }).eq('is_active', true),
    supabase.from('products').select('id', { count: 'exact' }).eq('product_type', 'digital'),
    supabase.from('product_packages').select('id', { count: 'exact' }),
    supabase.from('orders').select('id', { count: 'exact' }),
    supabase.from('products').select('category').groupBy('category')
  ])
  */
  
  // Temporary: Calculate from localStorage
  const products = await getProducts()

  // Ensure products is an array and has valid structure
  const validProducts = Array.isArray(products) ? products.filter(p => p && typeof p === 'object') : []

  return {
    totalProducts: validProducts.length,
    activeProducts: validProducts.filter(p => p.isActive === true).length,
    digitalProducts: validProducts.filter(p => p.productType === 'digital').length,
    physicalProducts: validProducts.filter(p => p.productType === 'physical').length,
    totalPackages: validProducts.reduce((sum, p) => {
      const packages = p.packages || []
      return sum + (Array.isArray(packages) ? packages.length : 0)
    }, 0),
    totalOrders: 0, // ## TODO: Get from orders table
    popularCategories: getPopularCategories(validProducts)
  }
}

// =====================================================
// HELPER FUNCTIONS
// =====================================================

/**
 * Apply filters to products array (temporary implementation)
 */
function applyFilters(products: ProductTemplate[], filters?: ProductFilters): ProductTemplate[] {
  // Ensure products is a valid array
  const validProducts = Array.isArray(products) ? products.filter(p => p && typeof p === 'object') : []

  if (!filters) return validProducts

  return validProducts.filter(product => {
    // Ensure product has required properties
    if (!product.name || !product.category) return false

    if (filters.category && product.category !== filters.category) return false
    if (filters.productType && product.productType !== filters.productType) return false
    if (filters.processingType && product.processingType !== filters.processingType) return false
    if (filters.isActive !== undefined && product.isActive !== filters.isActive) return false
    if (filters.isFeatured !== undefined && product.isFeatured !== filters.isFeatured) return false
    if (filters.search) {
      const searchLower = filters.search.toLowerCase()
      const nameMatch = product.name && product.name.toLowerCase().includes(searchLower)
      const descMatch = product.description && product.description.toLowerCase().includes(searchLower)
      if (!nameMatch && !descMatch) return false
    }
    return true
  })
}

/**
 * Get popular categories from products
 */
function getPopularCategories(products: ProductTemplate[]) {
  const categoryCount: Record<string, number> = {}

  // Ensure products is an array and filter valid products
  const validProducts = Array.isArray(products) ? products.filter(p => p && p.category) : []

  validProducts.forEach(product => {
    if (product.category && typeof product.category === 'string') {
      categoryCount[product.category] = (categoryCount[product.category] || 0) + 1
    }
  })

  return Object.entries(categoryCount)
    .map(([category, count]) => ({ category, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 5)
}

/**
 * Generate unique ID (temporary implementation)
 */
function generateId(): string {
  return Math.random().toString(36).substr(2, 9)
}

// =====================================================
// DATA TRANSFORMATION HELPERS
// =====================================================

/**
 * ## TODO: Transform database product to ProductTemplate interface
 */
function transformProductFromDB(dbProduct: any): ProductTemplate {
  // ## TODO: Implement transformation from Supabase row to ProductTemplate
  return dbProduct
}

/**
 * ## TODO: Transform database package to ProductPackage interface
 */
function transformPackageFromDB(dbPackage: any): ProductPackage {
  // ## TODO: Implement transformation from Supabase row to ProductPackage
  return dbPackage
}
