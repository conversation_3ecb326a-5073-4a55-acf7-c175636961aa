/**
 * Generate Sample Digital Codes Utility
 * Creates sample digital codes for testing and development
 */

import { encryptCode } from './encryption'

/**
 * Generate a random digital code
 */
function generateRandomCode(length: number = 12): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  
  return result
}

/**
 * Generate sample digital codes for a package
 */
export function generateSampleCodesForPackage(
  packageId: string, 
  count: number = 10,
  codeType: string = 'game_code'
): any[] {
  const codes = []
  
  for (let i = 0; i < count; i++) {
    const rawCode = generateRandomCode()
    const encryptedCode = encryptCode(rawCode)
    
    codes.push({
      id: `code_${packageId}_${i + 1}`,
      code: encryptedCode,
      rawCode: rawCode, // For development only - remove in production
      isUsed: false,
      isReserved: false,
      codeType,
      createdAt: new Date().toISOString(),
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days
    })
  }
  
  return codes
}

/**
 * Add sample codes to localStorage for testing
 */
export function addSampleCodesToPackage(packageId: string, count: number = 10): void {
  const storageKey = `digital_codes_${packageId}`
  const existingCodes = JSON.parse(localStorage.getItem(storageKey) || '[]')
  
  // Only add codes if none exist
  if (existingCodes.length === 0) {
    const newCodes = generateSampleCodesForPackage(packageId, count)
    localStorage.setItem(storageKey, JSON.stringify(newCodes))
    console.log(`✅ Added ${count} sample codes to package ${packageId}`)
  } else {
    console.log(`📦 Package ${packageId} already has ${existingCodes.length} codes`)
  }
}

/**
 * Initialize sample codes for all default packages
 */
export function initializeSampleCodes(): void {
  console.log('🔄 Initializing sample digital codes...')
  
  // Default package IDs from templates
  const defaultPackageIds = [
    'pubg-uc-60',
    'pubg-uc-325', 
    'pubg-uc-660',
    'pubg-uc-1800',
    'pubg-uc-3850',
    'ff-diamonds-100',
    'ff-diamonds-520',
    'ff-diamonds-1080',
    'ff-diamonds-2200',
    'gp-usd-10',
    'gp-usd-25',
    'gp-usd-50',
    'gp-usd-100',
    'tiktok-coins-100',
    'tiktok-coins-500',
    'tiktok-coins-1000',
    'tiktok-coins-2500'
  ]
  
  defaultPackageIds.forEach(packageId => {
    addSampleCodesToPackage(packageId, 5) // Add 5 codes per package
  })
  
  console.log('✅ Sample digital codes initialization complete')
}

/**
 * Clear all sample codes (for testing)
 */
export function clearAllSampleCodes(): void {
  const defaultPackageIds = [
    'pubg-uc-60', 'pubg-uc-325', 'pubg-uc-660', 'pubg-uc-1800', 'pubg-uc-3850',
    'ff-diamonds-100', 'ff-diamonds-520', 'ff-diamonds-1080', 'ff-diamonds-2200',
    'gp-usd-10', 'gp-usd-25', 'gp-usd-50', 'gp-usd-100',
    'tiktok-coins-100', 'tiktok-coins-500', 'tiktok-coins-1000', 'tiktok-coins-2500'
  ]
  
  defaultPackageIds.forEach(packageId => {
    localStorage.removeItem(`digital_codes_${packageId}`)
  })
  
  console.log('🗑️ All sample codes cleared')
}

/**
 * Get available codes count for a package
 */
export function getAvailableCodesCount(packageId: string): number {
  const storageKey = `digital_codes_${packageId}`
  const codes = JSON.parse(localStorage.getItem(storageKey) || '[]')
  
  return codes.filter((code: any) => 
    !code.isUsed && 
    !code.isReserved && 
    (!code.reservationExpiry || new Date(code.reservationExpiry) < new Date())
  ).length
}

/**
 * Check if package has sufficient codes
 */
export function hassufficientCodes(packageId: string, requiredCount: number): boolean {
  return getAvailableCodesCount(packageId) >= requiredCount
}

/**
 * Auto-initialize codes when needed
 */
export function ensureCodesAvailable(packageId: string, requiredCount: number = 1): void {
  const availableCount = getAvailableCodesCount(packageId)
  
  if (availableCount < requiredCount) {
    const neededCodes = Math.max(5, requiredCount * 2) // Generate at least 5 or double what's needed
    addSampleCodesToPackage(packageId, neededCodes)
    console.log(`🔄 Auto-generated ${neededCodes} codes for package ${packageId}`)
  }
}
