/**
 * Secure Wallet API Route
 * Demonstrates secure wallet operations with proper authentication and validation
 */

import { NextRequest, NextResponse } from 'next/server'
import { withAuth, withRateLimit, withValidation, withLogging, combineMiddlewares } from '@/lib/middleware/auth'
import { validateCurrencyAmount, validateEmail } from '@/lib/utils/validation'
import { atomicWalletTransaction } from '@/lib/utils/atomicOperations'
import { getCurrencyForUser } from '@/lib/utils/userPreferences'
import { Currency, Transaction } from '@/lib/types'

/**
 * Validation schema for wallet transactions
 */
function validateTransactionData(data: any): { isValid: boolean; errors?: string[]; data?: any } {
  const errors: string[] = []
  const sanitizedData: any = {}

  // Validate amount
  if (!data.amount || typeof data.amount !== 'number') {
    errors.push('Amount is required and must be a number')
  } else {
    const amountValidation = validateCurrencyAmount(data.amount, data.currency || 'USD')
    if (!amountValidation.isValid) {
      errors.push(amountValidation.error!)
    } else {
      sanitizedData.amount = amountValidation.sanitizedValue
    }
  }

  // Validate currency
  const validCurrencies: Currency[] = ['USD', 'SDG', 'EGP']
  if (!data.currency || !validCurrencies.includes(data.currency)) {
    errors.push('Valid currency is required (USD, SDG, EGP)')
  } else {
    sanitizedData.currency = data.currency
  }

  // Validate transaction type
  const validTypes = ['deposit', 'withdrawal', 'purchase']
  if (!data.type || !validTypes.includes(data.type)) {
    errors.push('Valid transaction type is required (deposit, withdrawal, purchase)')
  } else {
    sanitizedData.type = data.type
  }

  // Validate description
  if (!data.description || typeof data.description !== 'string') {
    errors.push('Description is required')
  } else {
    sanitizedData.description = data.description.trim().substring(0, 255)
  }

  // Validate user ID (for admin operations)
  if (data.userId) {
    const emailValidation = validateEmail(data.userId)
    if (!emailValidation.isValid) {
      errors.push('Valid user ID (email) is required')
    } else {
      sanitizedData.userId = emailValidation.sanitizedValue
    }
  }

  return {
    isValid: errors.length === 0,
    errors: errors.length > 0 ? errors : undefined,
    data: sanitizedData
  }
}

/**
 * GET /api/wallet/secure
 * Get wallet balance and recent transactions
 */
async function getWalletData(request: NextRequest, context: any) {
  try {
    const { searchParams } = new URL(request.url)
    const currency = searchParams.get('currency') as Currency || getCurrencyForUser(context.user.id)
    const limit = parseInt(searchParams.get('limit') || '10')

    // Validate parameters
    const validCurrencies: Currency[] = ['USD', 'SDG', 'EGP']
    if (!validCurrencies.includes(currency)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid currency' 
        },
        { status: 400 }
      )
    }

    if (limit < 1 || limit > 100) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Limit must be between 1 and 100' 
        },
        { status: 400 }
      )
    }

    // Get wallet data from localStorage (in production, use database)
    const walletKey = `wallet_${context.user.id}`
    const walletData = JSON.parse(localStorage.getItem(walletKey) || '{}')
    
    const transactionsKey = `transactions_${context.user.id}`
    const allTransactions = JSON.parse(localStorage.getItem(transactionsKey) || '[]')
    
    // Filter and sort transactions
    const recentTransactions = allTransactions
      .filter((t: Transaction) => t.currency === currency)
      .sort((a: Transaction, b: Transaction) => new Date(b.date).getTime() - new Date(a.date).getTime())
      .slice(0, limit)

    const response = {
      success: true,
      data: {
        balance: walletData.balances?.[currency] || 0,
        currency,
        transactions: recentTransactions,
        lastUpdated: walletData.lastUpdated || new Date().toISOString()
      }
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('Error fetching wallet data:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to fetch wallet data' 
      },
      { status: 500 }
    )
  }
}

/**
 * POST /api/wallet/secure
 * Create a new wallet transaction
 */
async function createTransaction(request: NextRequest, context: any, validatedData: any) {
  try {
    // Check if user is trying to perform transaction on their own wallet
    const targetUserId = validatedData.userId || context.user.id
    
    // Only allow users to transact on their own wallet, unless they're admin
    if (targetUserId !== context.user.id && context.user.role !== 'admin') {
      return NextResponse.json(
        { 
          success: false,
          error: 'Unauthorized: Cannot perform transactions on other users wallets' 
        },
        { status: 403 }
      )
    }

    // Additional validation for withdrawals
    if (validatedData.type === 'withdrawal') {
      const walletKey = `wallet_${targetUserId}`
      const walletData = JSON.parse(localStorage.getItem(walletKey) || '{}')
      const currentBalance = walletData.balances?.[validatedData.currency] || 0
      
      if (currentBalance < validatedData.amount) {
        return NextResponse.json(
          { 
            success: false,
            error: 'Insufficient balance' 
          },
          { status: 400 }
        )
      }
    }

    // Create transaction atomically
    const transactionData = {
      userId: targetUserId,
      walletId: `wallet_${targetUserId}`,
      type: validatedData.type,
      amount: validatedData.amount,
      currency: validatedData.currency,
      description: validatedData.description,
      status: 'completed' as const,
      referenceNumber: `ref_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    }

    const balanceUpdate = {
      currency: validatedData.currency,
      amount: validatedData.amount,
      type: validatedData.type === 'deposit' ? 'add' as const : 'subtract' as const
    }

    const result = await atomicWalletTransaction(
      targetUserId,
      transactionData,
      balanceUpdate
    )

    // Log the transaction
    console.log(`User ${context.user.email} created ${validatedData.type} transaction: ${validatedData.amount} ${validatedData.currency}`)

    return NextResponse.json({
      success: true,
      data: {
        transaction: result.transaction,
        newBalance: result.newBalance,
        message: 'Transaction completed successfully'
      }
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating transaction:', error)
    
    // Return user-friendly error message
    const errorMessage = error instanceof Error ? error.message : 'Failed to create transaction'
    
    return NextResponse.json(
      { 
        success: false,
        error: errorMessage
      },
      { status: 500 }
    )
  }
}

/**
 * GET /api/wallet/secure/balance
 * Get current balance for all currencies
 */
async function getAllBalances(request: NextRequest, context: any) {
  try {
    const walletKey = `wallet_${context.user.id}`
    const walletData = JSON.parse(localStorage.getItem(walletKey) || '{}')
    
    const balances = walletData.balances || {
      USD: 0,
      SDG: 0,
      EGP: 0
    }

    return NextResponse.json({
      success: true,
      data: {
        balances,
        preferredCurrency: getCurrencyForUser(context.user.id),
        lastUpdated: walletData.lastUpdated || new Date().toISOString()
      }
    })

  } catch (error) {
    console.error('Error fetching balances:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to fetch balances' 
      },
      { status: 500 }
    )
  }
}

// Create middleware stack for different security levels
const userAuthHandler = combineMiddlewares(
  withLogging,
  withRateLimit(50, 60000), // 50 requests per minute for regular users
  withAuth(undefined, { requireAuth: true })
)

const transactionHandler = combineMiddlewares(
  withLogging,
  withRateLimit(20, 60000), // 20 transactions per minute
  withAuth(undefined, { requireAuth: true, requiredPermissions: ['wallet:write'] })
)

// Route handlers with appropriate security
export const GET = userAuthHandler(getWalletData)
export const POST = transactionHandler(withValidation(validateTransactionData, createTransaction))

// Additional route for balance endpoint
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
