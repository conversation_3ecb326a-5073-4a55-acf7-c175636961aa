"use client"

import React, { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ShoppingCart, Zap, Calculator, Star } from "lucide-react"
import { ProductTemplate, ProductFormData, ProductPackage, Currency, DynamicField, enhancePackageWithDiscountInfo } from "@/lib/types"
import { formatCurrency } from "@/lib/data/currencies"
import { useCurrencyConverter } from "@/contexts/CurrencyContext"

interface SimpleProductFormProps {
  template: ProductTemplate
  onSubmit: (formData: ProductFormData) => void
  currency: Currency
  showPricing?: boolean
  disabled?: boolean
  className?: string
}

export function SimpleProductForm({
  template,
  onSubmit,
  currency,
  showPricing = true,
  disabled = false,
  className = ""
}: SimpleProductFormProps) {
  const [selectedPackage, setSelectedPackage] = useState<ProductPackage | null>(null)
  const [quantity, setQuantity] = useState(1)
  const [customFieldValues, setCustomFieldValues] = useState<Record<string, any>>({})
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({})
  const [isLoading, setIsLoading] = useState(false)
  
  const { convertPrice, formatPrice: contextFormatPrice, selectedCurrency } = useCurrencyConverter()

  // Calculate total price in the selected currency
  const calculateTotalPrice = () => {
    if (!selectedPackage) return 0
    const basePrice = selectedPackage.price * quantity
    return convertPrice(basePrice, currency)
  }

  // Format price with currency
  const formatPrice = (price: number) => {
    const convertedPrice = convertPrice(price, currency)
    return formatCurrency(convertedPrice, currency)
  }

  // Handle package selection
  const handlePackageSelect = (pkg: ProductPackage) => {
    setSelectedPackage(pkg)
    setFieldErrors({}) // Clear errors when package changes
  }

  // Handle custom field changes
  const handleFieldChange = (fieldName: string, value: any) => {
    setCustomFieldValues(prev => ({
      ...prev,
      [fieldName]: value
    }))
    
    // Clear error for this field when user starts typing
    if (fieldErrors[fieldName]) {
      setFieldErrors(prev => ({
        ...prev,
        [fieldName]: ""
      }))
    }
  }

  // Validate form fields
  const validateFields = () => {
    const errors: Record<string, string> = {}
    const activeFields = template.fields.filter(f => f.isActive)
    
    activeFields.forEach(field => {
      const value = customFieldValues[field.name]
      
      if (field.required && (!value || value.toString().trim() === "")) {
        errors[field.name] = `${field.label} مطلوب`
      }
      
      if (value && field.type === "email") {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        if (!emailRegex.test(value)) {
          errors[field.name] = "يرجى إدخال بريد إلكتروني صحيح"
        }
      }
      
      if (value && field.type === "number") {
        if (isNaN(Number(value))) {
          errors[field.name] = "يرجى إدخال رقم صحيح"
        }
      }
    })
    
    return errors
  }

  // Handle form submission
  const handleSubmit = async () => {
    if (!selectedPackage) {
      alert("يرجى اختيار حزمة")
      return
    }
    
    // Validate all custom fields
    const errors = validateFields()
    
    if (Object.keys(errors).length > 0) {
      setFieldErrors(errors)
      return
    }
    
    try {
      setIsLoading(true)
      
      const formData: ProductFormData = {
        templateId: template.id,
        selectedPackage,
        quantity,
        customFields: customFieldValues,
        totalPrice: calculateTotalPrice(),
        currency
      }
      
      await onSubmit(formData)
    } catch (error) {
      console.error("Error submitting form:", error)
      alert("حدث خطأ أثناء إرسال الطلب")
    } finally {
      setIsLoading(false)
    }
  }

  // Render custom field
  const renderCustomField = (field: DynamicField) => {
    const value = customFieldValues[field.name] || ""
    const error = fieldErrors[field.name]
    
    return (
      <div key={field.id} className="space-y-2">
        <label className="block text-sm font-medium text-slate-300">
          {field.label}
          {field.required && <span className="text-red-400 ml-1">*</span>}
        </label>
        
        {field.type === "universal_input" && (
          <input
            type="text"
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            placeholder={field.placeholder}
            disabled={disabled}
            className={`w-full bg-slate-700 border rounded-lg px-3 py-2 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              error ? "border-red-500" : "border-slate-600"
            }`}
          />
        )}
        
        {field.type === "dropdown" && (
          <select
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            disabled={disabled}
            className={`w-full bg-slate-700 border rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              error ? "border-red-500" : "border-slate-600"
            }`}
          >
            <option value="">
              {field.placeholder || `اختر ${field.label}`}
            </option>
            {field.options?.map((option) => (
              <option key={option.id} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        )}
        
        {/* Legacy support for old field types */}
        {field.type === "text" && (
          <input
            type="text"
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            placeholder={field.placeholder}
            disabled={disabled}
            className={`w-full bg-slate-700 border rounded-lg px-3 py-2 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              error ? "border-red-500" : "border-slate-600"
            }`}
          />
        )}
        
        {field.type === "email" && (
          <input
            type="email"
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            placeholder={field.placeholder}
            disabled={disabled}
            className={`w-full bg-slate-700 border rounded-lg px-3 py-2 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              error ? "border-red-500" : "border-slate-600"
            }`}
          />
        )}
        
        {field.type === "number" && (
          <input
            type="number"
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            placeholder={field.placeholder}
            disabled={disabled}
            className={`w-full bg-slate-700 border rounded-lg px-3 py-2 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              error ? "border-red-500" : "border-slate-600"
            }`}
          />
        )}
        
        {error && (
          <p className="text-red-400 text-sm">{error}</p>
        )}
      </div>
    )
  }

  // Calculate values for rendering
  const totalPrice = calculateTotalPrice()
  const visibleFields = template.fields.filter(f => f.isActive)

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Package Selection */}
      <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Zap className="h-5 w-5" />
            اختر الحزمة
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4">
            {template.packages.map((pkg) => {
              const enhancedPkg = enhancePackageWithDiscountInfo(pkg)
              const availableCodes = pkg.digitalCodes?.filter(code => !code.used).length || 0

              // Enhanced availability logic for different product types
              const isAvailable = (() => {
                if (!pkg.isActive) return false

                // Digital products with codes
                if (pkg.digitalCodes && pkg.digitalCodes.length > 0) {
                  return availableCodes > 0
                }

                // Products with manual quantity limits
                if (pkg.quantityLimit !== undefined && pkg.quantityLimit !== null) {
                  return pkg.quantityLimit > 0
                }

                // Unlimited digital products/services (no codes, no limits)
                return true
              })()
              
              return (
                <div
                  key={pkg.id}
                  onClick={() => !disabled && isAvailable && handlePackageSelect(pkg)}
                  className={`relative p-4 rounded-lg border-2 cursor-pointer transition-all ${
                    selectedPackage?.id === pkg.id
                      ? "border-blue-500 bg-blue-500/10"
                      : isAvailable 
                        ? "border-slate-600 bg-slate-700/30 hover:border-slate-500"
                        : "border-red-600/50 bg-red-900/20"
                  } ${disabled || !isAvailable ? "opacity-50 cursor-not-allowed" : ""}`}
                >
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <h3 className="font-semibold text-white">{pkg.name}</h3>
                      {pkg.amount && (
                        <p className="text-slate-300 text-sm">{pkg.amount}</p>
                      )}
                      {pkg.description && (
                        <p className="text-slate-400 text-sm mt-1">{pkg.description}</p>
                      )}
                      
                      {/* Enhanced Stock Status */}
                      {(() => {
                        // Digital products with codes
                        if (pkg.digitalCodes && pkg.digitalCodes.length > 0) {
                          return (
                            <p className={`text-xs mt-1 ${
                              availableCodes === 0 ? "text-red-400" :
                              availableCodes < 5 ? "text-yellow-400" : "text-green-400"
                            }`}>
                              {availableCodes === 0 ? "نفدت الكمية" :
                               availableCodes < 5 ? `متبقي ${availableCodes}` : "متوفر"}
                            </p>
                          )
                        }

                        // Products with manual quantity limits
                        if (pkg.quantityLimit !== undefined && pkg.quantityLimit !== null) {
                          return (
                            <p className={`text-xs mt-1 ${
                              pkg.quantityLimit === 0 ? "text-red-400" :
                              pkg.quantityLimit < 5 ? "text-yellow-400" : "text-green-400"
                            }`}>
                              {pkg.quantityLimit === 0 ? "نفدت الكمية" :
                               pkg.quantityLimit < 5 ? `متبقي ${pkg.quantityLimit}` : "متوفر"}
                            </p>
                          )
                        }

                        // Unlimited digital products/services - show as available
                        return (
                          <p className="text-xs mt-1 text-green-400">
                            متوفر
                          </p>
                        )
                      })()}
                    </div>
                    
                    <div className="text-right">
                      {enhancedPkg.hasDiscount && pkg.originalPrice && (
                        <p className="text-slate-400 text-sm line-through">
                          {formatPrice(pkg.originalPrice)}
                        </p>
                      )}
                      <p className="text-white font-bold">
                        {formatPrice(pkg.price)}
                      </p>
                      {enhancedPkg.hasDiscount && enhancedPkg.discountPercentage && (
                        <Badge variant="destructive" className="mt-1">
                          خصم {enhancedPkg.discountPercentage}%
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Custom Fields */}
      {visibleFields.length > 0 && (
        <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-white">معلومات إضافية</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {visibleFields.map(renderCustomField)}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Submit Button */}
      {selectedPackage && (
        <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
          <CardContent className="pt-6">
            <Button
              onClick={handleSubmit}
              disabled={disabled || isLoading}
              className="w-full bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 text-black font-bold py-4 text-lg"
              size="lg"
            >
              {isLoading ? (
                "جاري المعالجة..."
              ) : (
                <>
                  {template.processingType === "instant" ? "اشتري الآن" : "أضف للسلة"}
                  {showPricing && ` - ${formatPrice(totalPrice)}`}
                </>
              )}
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
