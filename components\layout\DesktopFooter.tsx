"use client"

import {
  Home,
  MessageCircle,
  User,
  Wallet,
  Store,
} from "lucide-react"
import { useGlobalChat } from '@/components/chat/GlobalChatProvider'

interface DesktopFooterProps {
  activeTab: string
  onTabChange: (tab: string) => void
}

export function DesktopFooter({ activeTab, onTabChange }: DesktopFooterProps) {
  const { openChat } = useGlobalChat()

  // Standardized navigation items matching mobile
  const navItems = [
    {
      id: "profile",
      icon: <User className="h-6 w-6" />,
      label: "حسابي",
      action: () => onTabChange("profile")
    },
    {
      id: "shop",
      icon: <Store className="h-6 w-6" />,
      label: "المتجر",
      action: () => onTabChange("shop")
    },
    {
      id: "home",
      icon: <Home className="h-6 w-6" />,
      label: "الرئيسية",
      action: () => onTabChange("home")
    },
    {
      id: "wallet",
      icon: <Wallet className="h-6 w-6" />,
      label: "المحفظة",
      action: () => onTabChange("wallet")
    },
    {
      id: "chat",
      icon: <MessageCircle className="h-6 w-6" />,
      label: "المحادثة",
      action: () => openChat() // Opens chat directly, consistent with mobile
    },
  ]

  return (
    <footer className="hidden lg:block relative z-10 bg-slate-800/50 backdrop-blur-xl border-t border-slate-700/50 mt-12">
      <div className="max-w-7xl mx-auto px-8 py-8">
        <div className="flex items-center justify-center gap-8">
          {navItems.map(({ id, icon, label, action }) => (
            <button
              key={id}
              onClick={action}
              className={`relative flex flex-col items-center gap-2 p-4 rounded-2xl transition-all duration-300 transform hover:scale-105 active:scale-95 min-w-[5rem] ${
                activeTab === id
                  ? "bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 shadow-lg shadow-yellow-500/25 ring-2 ring-yellow-400/20 scale-105"
                  : "text-slate-400 hover:text-white hover:bg-white/10 hover:shadow-md hover:ring-1 hover:ring-white/10"
              }`}
            >
              <div className="relative">
                {icon}
                {activeTab === id && (
                  <div className="absolute -inset-1 bg-white/20 rounded-full animate-pulse" />
                )}
              </div>
              <span className={`text-sm font-medium transition-all duration-300 whitespace-nowrap ${
                activeTab === id ? "text-slate-900 font-semibold" : "text-slate-400"
              }`}>
                {label}
              </span>

              {/* Active indicator */}
              {activeTab === id && (
                <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-slate-900 rounded-full animate-pulse shadow-lg" />
              )}
            </button>
          ))}
        </div>
      </div>
    </footer>
  )
}
