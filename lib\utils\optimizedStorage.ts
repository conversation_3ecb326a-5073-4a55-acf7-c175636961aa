/**
 * Optimized Storage Utility
 * Provides indexed, cached, and paginated access to localStorage data
 */

import { ProductTemplate, Order, Transaction } from '@/lib/types'

// In-memory indexes for fast lookups
const indexes = {
  products: new Map<string, ProductTemplate>(),
  orders: new Map<string, Order>(),
  transactions: new Map<string, Transaction>(),
  productsByCategory: new Map<string, string[]>(),
  ordersByUser: new Map<string, string[]>(),
  transactionsByUser: new Map<string, string[]>()
}

// Cache for frequently accessed data
const cache = {
  products: null as ProductTemplate[] | null,
  orders: null as Order[] | null,
  transactions: null as Transaction[] | null,
  lastUpdated: {
    products: 0,
    orders: 0,
    transactions: 0
  }
}

const CACHE_TTL = 5 * 60 * 1000 // 5 minutes

/**
 * Initialize indexes from localStorage
 */
export function initializeIndexes(): void {
  console.log('🔄 Initializing storage indexes...')
  
  try {
    // Index products
    const products = getFromStorage<ProductTemplate[]>('alraya_products', [])
    products.forEach(product => {
      indexes.products.set(product.id, product)
      
      // Index by category
      if (product.category) {
        const categoryProducts = indexes.productsByCategory.get(product.category) || []
        categoryProducts.push(product.id)
        indexes.productsByCategory.set(product.category, categoryProducts)
      }
    })

    // Index orders
    const orders = getFromStorage<Order[]>('alraya_orders', [])
    orders.forEach(order => {
      indexes.orders.set(order.id, order)
      
      // Index by user
      const userOrders = indexes.ordersByUser.get(order.userDetails.email) || []
      userOrders.push(order.id)
      indexes.ordersByUser.set(order.userDetails.email, userOrders)
    })

    // Index transactions
    const transactions = getFromStorage<Transaction[]>('alraya_transactions', [])
    transactions.forEach(transaction => {
      indexes.transactions.set(transaction.id, transaction)
      
      // Index by user
      const userTransactions = indexes.transactionsByUser.get(transaction.userId) || []
      userTransactions.push(transaction.id)
      indexes.transactionsByUser.set(transaction.userId, userTransactions)
    })

    console.log(`✅ Indexed ${products.length} products, ${orders.length} orders, ${transactions.length} transactions`)
  } catch (error) {
    console.error('Error initializing indexes:', error)
  }
}

/**
 * Safe localStorage operations with error handling
 */
function getFromStorage<T>(key: string, defaultValue: T): T {
  try {
    if (typeof window === 'undefined') return defaultValue
    const item = localStorage.getItem(key)
    return item ? JSON.parse(item) : defaultValue
  } catch (error) {
    console.error(`Error reading from localStorage key "${key}":`, error)
    return defaultValue
  }
}

function setToStorage<T>(key: string, value: T): boolean {
  try {
    if (typeof window === 'undefined') return false
    localStorage.setItem(key, JSON.stringify(value))
    return true
  } catch (error) {
    console.error(`Error writing to localStorage key "${key}":`, error)
    return false
  }
}

/**
 * Optimized product operations
 */
export class OptimizedProductStorage {
  /**
   * Get product by ID (O(1) lookup)
   */
  static getById(id: string): ProductTemplate | null {
    return indexes.products.get(id) || null
  }

  /**
   * Get all products with caching
   */
  static getAll(): ProductTemplate[] {
    const now = Date.now()
    
    if (cache.products && (now - cache.lastUpdated.products) < CACHE_TTL) {
      return cache.products
    }

    const products = getFromStorage<ProductTemplate[]>('alraya_products', [])
    cache.products = products
    cache.lastUpdated.products = now
    
    return products
  }

  /**
   * Get products by category (indexed lookup)
   */
  static getByCategory(category: string): ProductTemplate[] {
    const productIds = indexes.productsByCategory.get(category) || []
    return productIds.map(id => indexes.products.get(id)).filter(Boolean) as ProductTemplate[]
  }

  /**
   * Search products with pagination
   */
  static search(
    query: string,
    options: {
      page?: number
      limit?: number
      category?: string
      isActive?: boolean
    } = {}
  ): { products: ProductTemplate[]; total: number; hasMore: boolean } {
    const { page = 1, limit = 20, category, isActive } = options
    const offset = (page - 1) * limit

    let products = this.getAll()

    // Apply filters
    if (category) {
      products = this.getByCategory(category)
    }

    if (isActive !== undefined) {
      products = products.filter(p => p.isActive === isActive)
    }

    if (query) {
      const searchTerm = query.toLowerCase()
      products = products.filter(p =>
        p.name.toLowerCase().includes(searchTerm) ||
        p.description.toLowerCase().includes(searchTerm) ||
        p.tags.some(tag => tag.toLowerCase().includes(searchTerm))
      )
    }

    const total = products.length
    const paginatedProducts = products.slice(offset, offset + limit)
    const hasMore = offset + limit < total

    return {
      products: paginatedProducts,
      total,
      hasMore
    }
  }

  /**
   * Create product with index update
   */
  static create(productData: Omit<ProductTemplate, 'id' | 'createdAt' | 'updatedAt'>): ProductTemplate {
    const now = new Date()
    const newProduct: ProductTemplate = {
      ...productData,
      id: generateId('prod_'),
      createdAt: now,
      updatedAt: now
    }

    // Update storage
    const products = this.getAll()
    products.push(newProduct)
    setToStorage('alraya_products', products)

    // Update indexes
    indexes.products.set(newProduct.id, newProduct)
    if (newProduct.category) {
      const categoryProducts = indexes.productsByCategory.get(newProduct.category) || []
      categoryProducts.push(newProduct.id)
      indexes.productsByCategory.set(newProduct.category, categoryProducts)
    }

    // Invalidate cache
    cache.products = null

    return newProduct
  }

  /**
   * Update product with index update
   */
  static update(id: string, updates: Partial<ProductTemplate>): ProductTemplate | null {
    const existing = indexes.products.get(id)
    if (!existing) return null

    const updated = { ...existing, ...updates, updatedAt: new Date() }

    // Update storage
    const products = this.getAll()
    const index = products.findIndex(p => p.id === id)
    if (index >= 0) {
      products[index] = updated
      setToStorage('alraya_products', products)
    }

    // Update indexes
    indexes.products.set(id, updated)

    // Update category index if category changed
    if (updates.category && updates.category !== existing.category) {
      // Remove from old category
      if (existing.category) {
        const oldCategoryProducts = indexes.productsByCategory.get(existing.category) || []
        const filteredOld = oldCategoryProducts.filter(pid => pid !== id)
        indexes.productsByCategory.set(existing.category, filteredOld)
      }

      // Add to new category
      const newCategoryProducts = indexes.productsByCategory.get(updates.category) || []
      newCategoryProducts.push(id)
      indexes.productsByCategory.set(updates.category, newCategoryProducts)
    }

    // Invalidate cache
    cache.products = null

    return updated
  }
}

/**
 * Optimized order operations
 */
export class OptimizedOrderStorage {
  /**
   * Get order by ID (O(1) lookup)
   */
  static getById(id: string): Order | null {
    return indexes.orders.get(id) || null
  }

  /**
   * Get orders by user (indexed lookup)
   */
  static getByUser(userEmail: string): Order[] {
    const orderIds = indexes.ordersByUser.get(userEmail) || []
    return orderIds.map(id => indexes.orders.get(id)).filter(Boolean) as Order[]
  }

  /**
   * Get orders with pagination and filtering
   */
  static getWithPagination(
    options: {
      page?: number
      limit?: number
      status?: string
      userEmail?: string
      sortBy?: 'date' | 'amount'
      sortOrder?: 'asc' | 'desc'
    } = {}
  ): { orders: Order[]; total: number; hasMore: boolean } {
    const { page = 1, limit = 20, status, userEmail, sortBy = 'date', sortOrder = 'desc' } = options
    const offset = (page - 1) * limit

    let orders = cache.orders || getFromStorage<Order[]>('alraya_orders', [])

    // Apply filters
    if (userEmail) {
      orders = this.getByUser(userEmail)
    }

    if (status) {
      orders = orders.filter(o => o.status === status)
    }

    // Sort orders
    orders.sort((a, b) => {
      let comparison = 0
      
      if (sortBy === 'date') {
        comparison = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
      } else if (sortBy === 'amount') {
        comparison = a.totalPrice - b.totalPrice
      }

      return sortOrder === 'desc' ? -comparison : comparison
    })

    const total = orders.length
    const paginatedOrders = orders.slice(offset, offset + limit)
    const hasMore = offset + limit < total

    return {
      orders: paginatedOrders,
      total,
      hasMore
    }
  }
}

/**
 * Optimized transaction operations
 */
export class OptimizedTransactionStorage {
  /**
   * Get transaction by ID (O(1) lookup)
   */
  static getById(id: string): Transaction | null {
    return indexes.transactions.get(id) || null
  }

  /**
   * Get transactions by user (indexed lookup)
   */
  static getByUser(userId: string): Transaction[] {
    const transactionIds = indexes.transactionsByUser.get(userId) || []
    return transactionIds.map(id => indexes.transactions.get(id)).filter(Boolean) as Transaction[]
  }

  /**
   * Get recent transactions with limit
   */
  static getRecent(userId: string, limit: number = 10): Transaction[] {
    const userTransactions = this.getByUser(userId)
    return userTransactions
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
      .slice(0, limit)
  }
}

/**
 * Batch operations for better performance
 */
export class BatchOperations {
  /**
   * Batch create products
   */
  static createProducts(productsData: Array<Omit<ProductTemplate, 'id' | 'createdAt' | 'updatedAt'>>): ProductTemplate[] {
    const now = new Date()
    const newProducts = productsData.map(data => ({
      ...data,
      id: generateId('prod_'),
      createdAt: now,
      updatedAt: now
    }))

    // Update storage in one operation
    const existingProducts = OptimizedProductStorage.getAll()
    const allProducts = [...existingProducts, ...newProducts]
    setToStorage('alraya_products', allProducts)

    // Update indexes
    newProducts.forEach(product => {
      indexes.products.set(product.id, product)
      if (product.category) {
        const categoryProducts = indexes.productsByCategory.get(product.category) || []
        categoryProducts.push(product.id)
        indexes.productsByCategory.set(product.category, categoryProducts)
      }
    })

    // Invalidate cache
    cache.products = null

    return newProducts
  }
}

/**
 * Storage health monitoring
 */
export function getStorageHealth(): {
  size: number
  itemCount: number
  indexHealth: {
    products: number
    orders: number
    transactions: number
  }
  cacheStatus: {
    products: boolean
    orders: boolean
    transactions: boolean
  }
} {
  let totalSize = 0
  let itemCount = 0

  // Calculate storage size
  for (let key in localStorage) {
    if (localStorage.hasOwnProperty(key)) {
      totalSize += localStorage[key].length
      itemCount++
    }
  }

  return {
    size: totalSize,
    itemCount,
    indexHealth: {
      products: indexes.products.size,
      orders: indexes.orders.size,
      transactions: indexes.transactions.size
    },
    cacheStatus: {
      products: cache.products !== null,
      orders: cache.orders !== null,
      transactions: cache.transactions !== null
    }
  }
}

/**
 * Clear all caches
 */
export function clearCaches(): void {
  cache.products = null
  cache.orders = null
  cache.transactions = null
  console.log('✅ All caches cleared')
}

/**
 * Generate unique ID
 */
function generateId(prefix: string = ''): string {
  return `${prefix}${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}
