/**
 * Precision Currency Utilities
 * Handles financial calculations with decimal precision to avoid floating-point errors
 */

import { Currency } from '@/lib/types'

// Use integer arithmetic with fixed decimal places to avoid floating-point errors
const DECIMAL_PLACES = 4 // Support up to 4 decimal places for precision
const MULTIPLIER = Math.pow(10, DECIMAL_PLACES)

/**
 * Convert decimal number to integer for precise calculations
 */
function toInteger(value: number): number {
  return Math.round(value * MULTIPLIER)
}

/**
 * Convert integer back to decimal
 */
function toDecimal(value: number): number {
  return value / MULTIPLIER
}

/**
 * Precise currency addition
 */
export function addCurrency(a: number, b: number): number {
  const intA = toInteger(a)
  const intB = toInteger(b)
  return toDecimal(intA + intB)
}

/**
 * Precise currency subtraction
 */
export function subtractCurrency(a: number, b: number): number {
  const intA = toInteger(a)
  const intB = toInteger(b)
  return toDecimal(intA - intB)
}

/**
 * Precise currency multiplication
 */
export function multiplyCurrency(a: number, b: number): number {
  const intA = toInteger(a)
  const intB = toInteger(b)
  return toDecimal((intA * intB) / MULTIPLIER)
}

/**
 * Precise currency division
 */
export function divideCurrency(a: number, b: number): number {
  if (b === 0) {
    throw new Error('Division by zero')
  }
  const intA = toInteger(a)
  const intB = toInteger(b)
  return toDecimal((intA * MULTIPLIER) / intB)
}

/**
 * Precise currency conversion with exchange rates
 */
export function convertCurrencyPrecise(
  amount: number,
  fromRate: number,
  toRate: number
): number {
  if (fromRate === 0 || toRate === 0) {
    throw new Error('Invalid exchange rate')
  }
  
  // Convert to base currency first, then to target currency
  const baseAmount = divideCurrency(amount, fromRate)
  return multiplyCurrency(baseAmount, toRate)
}

/**
 * Round currency to appropriate decimal places
 */
export function roundCurrency(amount: number, currency: Currency): number {
  const decimalPlaces = getCurrencyDecimalPlaces(currency)
  const factor = Math.pow(10, decimalPlaces)
  return Math.round(amount * factor) / factor
}

/**
 * Get decimal places for currency
 */
function getCurrencyDecimalPlaces(currency: Currency): number {
  switch (currency) {
    case 'USD':
    case 'EGP':
      return 2
    case 'SDG':
      return 2
    default:
      return 2
  }
}

/**
 * Compare currency amounts with precision tolerance
 */
export function compareCurrency(a: number, b: number, tolerance: number = 0.01): number {
  const diff = subtractCurrency(a, b)
  if (Math.abs(diff) < tolerance) {
    return 0 // Equal within tolerance
  }
  return diff > 0 ? 1 : -1
}

/**
 * Check if currency amount is equal within tolerance
 */
export function isCurrencyEqual(a: number, b: number, tolerance: number = 0.01): boolean {
  return compareCurrency(a, b, tolerance) === 0
}

/**
 * Validate currency amount
 */
export function validateCurrencyAmount(amount: number): boolean {
  if (typeof amount !== 'number' || isNaN(amount) || !isFinite(amount)) {
    return false
  }
  
  if (amount < 0) {
    return false
  }
  
  // Check for reasonable maximum (prevent overflow)
  if (amount > 1000000000) { // 1 billion
    return false
  }
  
  return true
}

/**
 * Format currency amount for display
 */
export function formatCurrencyPrecise(
  amount: number,
  currency: Currency,
  locale: string = 'en-US'
): string {
  if (!validateCurrencyAmount(amount)) {
    return '0.00'
  }
  
  const rounded = roundCurrency(amount, currency)
  const decimalPlaces = getCurrencyDecimalPlaces(currency)
  
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: decimalPlaces,
    maximumFractionDigits: decimalPlaces
  }).format(rounded)
}

/**
 * Parse currency string to number
 */
export function parseCurrencyString(value: string): number {
  if (!value || typeof value !== 'string') {
    return 0
  }
  
  // Remove currency symbols and formatting
  const cleaned = value.replace(/[^\d.-]/g, '')
  const parsed = parseFloat(cleaned)
  
  return validateCurrencyAmount(parsed) ? parsed : 0
}

/**
 * Calculate percentage with precision
 */
export function calculatePercentage(amount: number, percentage: number): number {
  return multiplyCurrency(amount, percentage / 100)
}

/**
 * Apply discount with precision
 */
export function applyDiscount(amount: number, discountPercent: number): number {
  const discount = calculatePercentage(amount, discountPercent)
  return subtractCurrency(amount, discount)
}

/**
 * Calculate tax with precision
 */
export function calculateTax(amount: number, taxPercent: number): number {
  return calculatePercentage(amount, taxPercent)
}

/**
 * Add tax to amount with precision
 */
export function addTax(amount: number, taxPercent: number): number {
  const tax = calculateTax(amount, taxPercent)
  return addCurrency(amount, tax)
}

/**
 * Split amount into parts with precision
 */
export function splitAmount(totalAmount: number, parts: number): number[] {
  if (parts <= 0) {
    throw new Error('Parts must be greater than 0')
  }
  
  const baseAmount = divideCurrency(totalAmount, parts)
  const result: number[] = new Array(parts).fill(baseAmount)
  
  // Handle remainder due to rounding
  const totalCalculated = multiplyCurrency(baseAmount, parts)
  const remainder = subtractCurrency(totalAmount, totalCalculated)
  
  if (remainder !== 0) {
    // Add remainder to first part
    result[0] = addCurrency(result[0], remainder)
  }
  
  return result
}

/**
 * Sum array of currency amounts with precision
 */
export function sumCurrencyAmounts(amounts: number[]): number {
  return amounts.reduce((sum, amount) => addCurrency(sum, amount), 0)
}

/**
 * Find maximum currency amount
 */
export function maxCurrencyAmount(amounts: number[]): number {
  if (amounts.length === 0) {
    return 0
  }
  
  return amounts.reduce((max, amount) => 
    compareCurrency(amount, max) > 0 ? amount : max
  )
}

/**
 * Find minimum currency amount
 */
export function minCurrencyAmount(amounts: number[]): number {
  if (amounts.length === 0) {
    return 0
  }
  
  return amounts.reduce((min, amount) => 
    compareCurrency(amount, min) < 0 ? amount : min
  )
}

/**
 * Calculate average of currency amounts
 */
export function averageCurrencyAmount(amounts: number[]): number {
  if (amounts.length === 0) {
    return 0
  }
  
  const total = sumCurrencyAmounts(amounts)
  return divideCurrency(total, amounts.length)
}

/**
 * Batch currency conversion with precision
 */
export function batchConvertCurrency(
  amounts: number[],
  fromRate: number,
  toRate: number
): number[] {
  return amounts.map(amount => convertCurrencyPrecise(amount, fromRate, toRate))
}

/**
 * Currency calculation result with metadata
 */
export interface CurrencyCalculationResult {
  result: number
  precision: number
  currency: Currency
  calculation: string
  timestamp: Date
}

/**
 * Perform currency calculation with audit trail
 */
export function performCurrencyCalculation(
  operation: string,
  operands: number[],
  currency: Currency
): CurrencyCalculationResult {
  let result: number
  let calculation: string
  
  switch (operation) {
    case 'add':
      result = operands.reduce((sum, val) => addCurrency(sum, val), 0)
      calculation = operands.join(' + ')
      break
    case 'subtract':
      result = operands.reduce((diff, val, index) => 
        index === 0 ? val : subtractCurrency(diff, val)
      )
      calculation = operands.join(' - ')
      break
    case 'multiply':
      result = operands.reduce((product, val) => multiplyCurrency(product, val), 1)
      calculation = operands.join(' × ')
      break
    case 'divide':
      result = operands.reduce((quotient, val, index) => 
        index === 0 ? val : divideCurrency(quotient, val)
      )
      calculation = operands.join(' ÷ ')
      break
    default:
      throw new Error(`Unsupported operation: ${operation}`)
  }
  
  return {
    result: roundCurrency(result, currency),
    precision: getCurrencyDecimalPlaces(currency),
    currency,
    calculation,
    timestamp: new Date()
  }
}
