/**
 * Comprehensive validation utilities for all forms and inputs
 * Provides input sanitization, business rule validation, and error handling
 * Standardized validation patterns across the application
 */

import { ProductTemplate, ProductPackage, DynamicField, DigitalCode, DropdownOption, Currency } from '@/lib/types'

// Validation result interface
export interface ValidationResult {
  isValid: boolean
  error?: string
  sanitizedValue?: any
  warnings?: string[]
}

// Batch validation result
export interface BatchValidationResult {
  isValid: boolean
  errors: Record<string, string>
  sanitizedData: Record<string, any>
  warnings: Record<string, string[]>
}

// =====================================================
// INPUT SANITIZATION
// =====================================================

/**
 * Sanitize text input to prevent XSS and clean up whitespace
 */
export const sanitizeText = (input: string, maxLength: number = 1000): string => {
  if (!input) return ''

  return input
    .trim()
    .replace(/\s+/g, ' ') // Replace multiple spaces with single space
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/[^\u0000-\u007F\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/g, '') // Allow only ASCII and Arabic
    .substring(0, maxLength) // Limit length to prevent abuse
}

/**
 * Sanitize HTML content (more aggressive)
 */
export const sanitizeHtml = (input: string): string => {
  if (!input) return ''

  return input
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove script tags
    .replace(/<[^>]*>/g, '') // Remove all HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocols
    .replace(/on\w+\s*=/gi, '') // Remove event handlers
    .trim()
}

/**
 * Validate and sanitize email
 */
export const validateEmail = (email: string): ValidationResult => {
  const sanitized = sanitizeText(email, 254).toLowerCase()

  if (!sanitized) {
    return { isValid: false, error: 'البريد الإلكتروني مطلوب' }
  }

  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
  if (!emailRegex.test(sanitized)) {
    return { isValid: false, error: 'البريد الإلكتروني غير صحيح' }
  }

  // Check for common disposable email domains
  const disposableDomains = ['tempmail.org', '10minutemail.com', 'guerrillamail.com']
  const domain = sanitized.split('@')[1]
  if (disposableDomains.includes(domain)) {
    return {
      isValid: true,
      sanitizedValue: sanitized,
      warnings: ['يبدو أن هذا بريد إلكتروني مؤقت']
    }
  }

  return { isValid: true, sanitizedValue: sanitized }
}

/**
 * Validate phone number
 */
export const validatePhone = (phone: string): ValidationResult => {
  const sanitized = phone.replace(/[^\d+]/g, '') // Keep only digits and +

  if (!sanitized) {
    return { isValid: false, error: 'رقم الهاتف مطلوب' }
  }

  // Basic international phone validation
  const phoneRegex = /^\+?[1-9]\d{7,14}$/
  if (!phoneRegex.test(sanitized)) {
    return { isValid: false, error: 'رقم الهاتف غير صحيح' }
  }

  return { isValid: true, sanitizedValue: sanitized }
}

/**
 * Validate currency amount
 */
export const validateCurrencyAmount = (amount: string | number, currency: Currency): ValidationResult => {
  const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount

  if (isNaN(numericAmount)) {
    return { isValid: false, error: 'المبلغ يجب أن يكون رقماً صحيحاً' }
  }

  if (numericAmount < 0) {
    return { isValid: false, error: 'المبلغ لا يمكن أن يكون سالباً' }
  }

  if (numericAmount > 1000000) {
    return { isValid: false, error: 'المبلغ كبير جداً' }
  }

  // Check decimal places based on currency
  const decimalPlaces = currency === 'SDG' ? 2 : 2
  const factor = Math.pow(10, decimalPlaces)
  const rounded = Math.round(numericAmount * factor) / factor

  return { isValid: true, sanitizedValue: rounded }
}

/**
 * Validate password strength
 */
export const validatePassword = (password: string): ValidationResult => {
  if (!password) {
    return { isValid: false, error: 'كلمة المرور مطلوبة' }
  }

  if (password.length < 8) {
    return { isValid: false, error: 'كلمة المرور يجب أن تكون 8 أحرف على الأقل' }
  }

  if (password.length > 128) {
    return { isValid: false, error: 'كلمة المرور طويلة جداً' }
  }

  const warnings: string[] = []

  if (!/[a-z]/.test(password)) {
    warnings.push('يُنصح بإضافة أحرف صغيرة')
  }

  if (!/[A-Z]/.test(password)) {
    warnings.push('يُنصح بإضافة أحرف كبيرة')
  }

  if (!/\d/.test(password)) {
    warnings.push('يُنصح بإضافة أرقام')
  }

  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    warnings.push('يُنصح بإضافة رموز خاصة')
  }

  // Check for common weak passwords
  const weakPasswords = ['password', '123456', 'qwerty', 'admin', 'letmein']
  if (weakPasswords.includes(password.toLowerCase())) {
    return { isValid: false, error: 'كلمة المرور ضعيفة جداً' }
  }

  return {
    isValid: true,
    sanitizedValue: password,
    warnings: warnings.length > 0 ? warnings : undefined
  }
}

/**
 * Validate URL
 */
export const validateUrl = (url: string): ValidationResult => {
  const sanitized = sanitizeText(url, 2048)

  if (!sanitized) {
    return { isValid: false, error: 'الرابط مطلوب' }
  }

  try {
    const urlObj = new URL(sanitized)

    // Only allow http and https protocols
    if (!['http:', 'https:'].includes(urlObj.protocol)) {
      return { isValid: false, error: 'الرابط يجب أن يبدأ بـ http أو https' }
    }

    return { isValid: true, sanitizedValue: sanitized }
  } catch {
    return { isValid: false, error: 'الرابط غير صحيح' }
  }
}

/**
 * Sanitize and validate digital code
 */
export const sanitizeDigitalCode = (code: string): string => {
  if (!code) return ''
  
  return code
    .trim()
    .toUpperCase()
    .replace(/[^A-Z0-9\-_]/g, '') // Only allow alphanumeric, dash, underscore
    .substring(0, 50) // Reasonable code length limit
}

/**
 * Sanitize numeric input
 */
export const sanitizeNumber = (input: string | number): number => {
  const num = typeof input === 'string' ? parseFloat(input) : input
  return isNaN(num) ? 0 : Math.max(0, num)
}

// =====================================================
// VALIDATION FUNCTIONS
// =====================================================

/**
 * Validate product name
 */
export const validateProductName = (name: string): { isValid: boolean; error?: string } => {
  const sanitized = sanitizeText(name)
  
  if (!sanitized) {
    return { isValid: false, error: 'يرجى إدخال اسم المنتج' }
  }
  
  if (sanitized.length < 3) {
    return { isValid: false, error: 'اسم المنتج يجب أن يكون 3 أحرف على الأقل' }
  }
  
  if (sanitized.length > 100) {
    return { isValid: false, error: 'اسم المنتج يجب أن يكون أقل من 100 حرف' }
  }
  
  return { isValid: true }
}

/**
 * Validate product category
 */
export const validateProductCategory = (category: string): { isValid: boolean; error?: string } => {
  const sanitized = sanitizeText(category)
  
  if (!sanitized) {
    return { isValid: false, error: 'يرجى إدخال فئة المنتج' }
  }
  
  if (sanitized.length < 2) {
    return { isValid: false, error: 'فئة المنتج يجب أن تكون حرفين على الأقل' }
  }
  
  return { isValid: true }
}

/**
 * Validate package name
 */
export const validatePackageName = (name: string): { isValid: boolean; error?: string } => {
  const sanitized = sanitizeText(name)
  
  if (!sanitized) {
    return { isValid: false, error: 'يرجى إدخال اسم الحزمة' }
  }
  
  if (sanitized.length < 2) {
    return { isValid: false, error: 'اسم الحزمة يجب أن يكون حرفين على الأقل' }
  }
  
  return { isValid: true }
}

/**
 * Validate package price
 */
export const validatePackagePrice = (price: number): { isValid: boolean; error?: string } => {
  if (!price || price <= 0) {
    return { isValid: false, error: 'يرجى إدخال سعر صحيح أكبر من صفر' }
  }
  
  if (price > 10000) {
    return { isValid: false, error: 'السعر يجب أن يكون أقل من 10,000' }
  }
  
  if (price < 0.01) {
    return { isValid: false, error: 'السعر يجب أن يكون 0.01 على الأقل' }
  }
  
  return { isValid: true }
}

/**
 * Validate digital codes array
 */
export const validateDigitalCodes = (codes: string[]): { 
  isValid: boolean; 
  error?: string; 
  sanitizedCodes?: string[];
  duplicates?: string[];
} => {
  if (!codes || codes.length === 0) {
    return { isValid: true, sanitizedCodes: [] } // Digital codes are optional
  }
  
  const sanitizedCodes = codes
    .map(sanitizeDigitalCode)
    .filter(Boolean) // Remove empty codes
  
  if (sanitizedCodes.length === 0) {
    return { isValid: true, sanitizedCodes: [] }
  }
  
  // Check for duplicates
  const duplicates = sanitizedCodes.filter((code, index) => 
    sanitizedCodes.indexOf(code) !== index
  )
  
  if (duplicates.length > 0) {
    return { 
      isValid: false, 
      error: `أكواد مكررة: ${duplicates.join(', ')}`,
      duplicates: [...new Set(duplicates)]
    }
  }
  
  // Check code format
  const invalidCodes = sanitizedCodes.filter(code => 
    code.length < 3 || code.length > 50
  )
  
  if (invalidCodes.length > 0) {
    return { 
      isValid: false, 
      error: `أكواد غير صالحة (يجب أن تكون بين 3-50 حرف): ${invalidCodes.join(', ')}`
    }
  }
  
  return { isValid: true, sanitizedCodes }
}

/**
 * Validate field label
 */
export const validateFieldLabel = (label: string): { isValid: boolean; error?: string } => {
  const sanitized = sanitizeText(label)
  
  if (!sanitized) {
    return { isValid: false, error: 'يرجى إدخال تسمية الحقل' }
  }
  
  if (sanitized.length < 2) {
    return { isValid: false, error: 'تسمية الحقل يجب أن تكون حرفين على الأقل' }
  }
  
  return { isValid: true }
}

/**
 * Validate dropdown options
 */
export const validateDropdownOptions = (options: DropdownOption[]): { 
  isValid: boolean; 
  error?: string;
  duplicates?: string[];
} => {
  if (!options || options.length === 0) {
    return { isValid: false, error: 'يرجى إضافة خيار واحد على الأقل للقائمة المنسدلة' }
  }
  
  // Check for empty labels
  const emptyOptions = options.filter(opt => !sanitizeText(opt.label))
  if (emptyOptions.length > 0) {
    return { isValid: false, error: 'جميع خيارات القائمة يجب أن تحتوي على نص' }
  }
  
  // Check for duplicates
  const labels = options.map(opt => sanitizeText(opt.label))
  const duplicates = labels.filter((label, index) => labels.indexOf(label) !== index)
  
  if (duplicates.length > 0) {
    return { 
      isValid: false, 
      error: `خيارات مكررة: ${[...new Set(duplicates)].join(', ')}`,
      duplicates: [...new Set(duplicates)]
    }
  }
  
  return { isValid: true }
}

/**
 * Validate complete product data
 */
export const validateProductData = (data: Partial<ProductTemplate>): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} => {
  const errors: string[] = []
  const warnings: string[] = []
  
  // Validate basic info
  const nameValidation = validateProductName(data.name || '')
  if (!nameValidation.isValid) {
    errors.push(nameValidation.error!)
  }
  
  const categoryValidation = validateProductCategory(data.category || '')
  if (!categoryValidation.isValid) {
    errors.push(categoryValidation.error!)
  }
  
  // Validate packages
  if (!data.packages || data.packages.length === 0) {
    errors.push('يرجى إضافة حزمة واحدة على الأقل')
  } else {
    data.packages.forEach((pkg, index) => {
      const nameValidation = validatePackageName(pkg.name)
      if (!nameValidation.isValid) {
        errors.push(`الحزمة ${index + 1}: ${nameValidation.error}`)
      }
      
      const priceValidation = validatePackagePrice(pkg.price)
      if (!priceValidation.isValid) {
        errors.push(`الحزمة ${index + 1}: ${priceValidation.error}`)
      }
    })
    
    // Check for duplicate package names
    const packageNames = data.packages.map(pkg => sanitizeText(pkg.name))
    const duplicateNames = packageNames.filter((name, index) => packageNames.indexOf(name) !== index)
    if (duplicateNames.length > 0) {
      errors.push(`أسماء حزم مكررة: ${[...new Set(duplicateNames)].join(', ')}`)
    }
  }
  
  // Validate fields
  if (data.fields && data.fields.length > 0) {
    data.fields.forEach((field, index) => {
      const labelValidation = validateFieldLabel(field.label)
      if (!labelValidation.isValid) {
        errors.push(`الحقل ${index + 1}: ${labelValidation.error}`)
      }
      
      // Validate dropdown options if it's a dropdown field
      if (field.type === 'dropdown' && field.options) {
        const optionsValidation = validateDropdownOptions(field.options)
        if (!optionsValidation.isValid) {
          errors.push(`الحقل ${index + 1}: ${optionsValidation.error}`)
        }
      }
    })
  }
  
  // Add warnings
  if (!data.description || data.description.trim().length < 10) {
    warnings.push('يُنصح بإضافة وصف مفصل للمنتج')
  }
  
  if (!data.image) {
    warnings.push('يُنصح بإضافة صورة للمنتج')
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

// =====================================================
// BUSINESS RULE VALIDATION
// =====================================================

/**
 * Check if packages have reasonable price distribution
 */
export const validatePriceDistribution = (packages: ProductPackage[]): {
  isValid: boolean;
  warnings: string[];
} => {
  const warnings: string[] = []
  
  if (packages.length < 2) {
    return { isValid: true, warnings }
  }
  
  const prices = packages.map(pkg => pkg.price).sort((a, b) => a - b)
  const minPrice = prices[0]
  const maxPrice = prices[prices.length - 1]
  
  // Check if price range is too narrow or too wide
  if (maxPrice / minPrice > 50) {
    warnings.push('فرق الأسعار بين الحزم كبير جداً - قد يسبب ارتباك للعملاء')
  }
  
  if (maxPrice / minPrice < 1.5 && packages.length > 3) {
    warnings.push('الأسعار متقاربة جداً - قد لا يكون هناك حافز لشراء الحزم الأكبر')
  }
  
  return { isValid: true, warnings }
}
