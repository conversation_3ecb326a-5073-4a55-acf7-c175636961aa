/**
 * Atomic Operations Utility
 * Provides thread-safe operations for localStorage and future database integration
 */

import { Transaction, WalletBalance, Currency } from '@/lib/types'

// Lock mechanism for preventing race conditions
const operationLocks = new Map<string, Promise<any>>()

/**
 * Execute operation with lock to prevent race conditions
 */
async function withLock<T>(lockKey: string, operation: () => Promise<T>): Promise<T> {
  // Wait for any existing operation with the same key
  if (operationLocks.has(lockKey)) {
    await operationLocks.get(lockKey)
  }

  // Create new operation promise
  const operationPromise = operation()
  operationLocks.set(lockKey, operationPromise)

  try {
    const result = await operationPromise
    return result
  } finally {
    // Clean up lock
    operationLocks.delete(lockKey)
  }
}

/**
 * Atomic wallet balance update with transaction creation
 */
export async function atomicWalletTransaction(
  userId: string,
  transaction: Omit<Transaction, 'id' | 'date'>,
  balanceUpdate: {
    currency: Currency
    amount: number
    type: 'add' | 'subtract'
  }
): Promise<{ transaction: Transaction; newBalance: number }> {
  const lockKey = `wallet_${userId}_${transaction.currency}`

  return withLock(lockKey, async () => {
    // Get current balance
    const currentBalance = getCurrentWalletBalance(userId, transaction.currency)
    
    // Validate sufficient funds for withdrawals/purchases
    if (balanceUpdate.type === 'subtract' && currentBalance < balanceUpdate.amount) {
      throw new Error('Insufficient balance')
    }

    // Calculate new balance
    const newBalance = balanceUpdate.type === 'add' 
      ? currentBalance + balanceUpdate.amount
      : currentBalance - balanceUpdate.amount

    // Create transaction record
    const newTransaction: Transaction = {
      ...transaction,
      id: generateTransactionId(),
      date: new Date()
    }

    // Atomic update: both balance and transaction must succeed
    try {
      // Update balance
      updateWalletBalanceAtomic(userId, transaction.currency, newBalance)
      
      // Add transaction
      addTransactionAtomic(newTransaction)

      // Dispatch events for UI updates
      window.dispatchEvent(new CustomEvent('walletUpdated', {
        detail: { userId, currency: transaction.currency, newBalance }
      }))

      window.dispatchEvent(new CustomEvent('transactionAdded', {
        detail: { transaction: newTransaction }
      }))

      return { transaction: newTransaction, newBalance }
    } catch (error) {
      // Rollback on failure (in localStorage, we'd need to restore previous state)
      console.error('Atomic wallet transaction failed:', error)
      throw new Error('Transaction failed - please try again')
    }
  })
}

/**
 * Atomic digital code reservation
 */
export async function atomicCodeReservation(
  productId: string,
  packageId: string,
  orderId: string,
  reservationTimeoutMs: number = 300000 // 5 minutes
): Promise<{ codeId: string; code: string } | null> {
  const lockKey = `codes_${productId}_${packageId}`

  return withLock(lockKey, async () => {
    // Get available codes
    const availableCodes = getAvailableDigitalCodes(productId, packageId)
    
    if (availableCodes.length === 0) {
      return null
    }

    // Reserve the first available code
    const codeToReserve = availableCodes[0]
    const reservationExpiry = new Date(Date.now() + reservationTimeoutMs)

    // Mark as reserved
    reserveDigitalCodeAtomic(productId, packageId, codeToReserve.id, orderId, reservationExpiry)

    // Set timeout to release reservation if not confirmed
    setTimeout(() => {
      releaseReservationIfNotUsed(productId, packageId, codeToReserve.id, orderId)
    }, reservationTimeoutMs)

    return {
      codeId: codeToReserve.id,
      code: codeToReserve.code
    }
  })
}

/**
 * Confirm code reservation (convert to permanent assignment)
 */
export async function confirmCodeReservation(
  productId: string,
  packageId: string,
  codeId: string,
  orderId: string
): Promise<boolean> {
  const lockKey = `codes_${productId}_${packageId}`

  return withLock(lockKey, async () => {
    return confirmDigitalCodeAssignment(productId, packageId, codeId, orderId)
  })
}

// Helper functions for localStorage operations
function getCurrentWalletBalance(userId: string, currency: Currency): number {
  try {
    const walletData = JSON.parse(localStorage.getItem(`wallet_${userId}`) || '{}')
    return walletData.balances?.[currency] || 0
  } catch {
    return 0
  }
}

function updateWalletBalanceAtomic(userId: string, currency: Currency, newBalance: number): void {
  const walletKey = `wallet_${userId}`
  const walletData = JSON.parse(localStorage.getItem(walletKey) || '{}')
  
  if (!walletData.balances) {
    walletData.balances = {}
  }
  
  walletData.balances[currency] = newBalance
  walletData.lastUpdated = new Date().toISOString()
  
  localStorage.setItem(walletKey, JSON.stringify(walletData))
}

function addTransactionAtomic(transaction: Transaction): void {
  const transactionsKey = `transactions_${transaction.userId}`
  const transactions = JSON.parse(localStorage.getItem(transactionsKey) || '[]')
  
  transactions.unshift(transaction) // Add to beginning
  
  // Keep only last 1000 transactions to prevent localStorage overflow
  if (transactions.length > 1000) {
    transactions.splice(1000)
  }
  
  localStorage.setItem(transactionsKey, JSON.stringify(transactions))
}

function getAvailableDigitalCodes(productId: string, packageId: string): any[] {
  const codesKey = `digital_codes_${packageId}`
  const codes = JSON.parse(localStorage.getItem(codesKey) || '[]')
  
  return codes.filter((code: any) => 
    !code.isUsed && 
    !code.isReserved && 
    (!code.reservationExpiry || new Date(code.reservationExpiry) < new Date())
  )
}

function reserveDigitalCodeAtomic(
  productId: string,
  packageId: string,
  codeId: string,
  orderId: string,
  expiry: Date
): void {
  const codesKey = `digital_codes_${packageId}`
  const codes = JSON.parse(localStorage.getItem(codesKey) || '[]')
  
  const codeIndex = codes.findIndex((code: any) => code.id === codeId)
  if (codeIndex >= 0) {
    codes[codeIndex].isReserved = true
    codes[codeIndex].reservedForOrderId = orderId
    codes[codeIndex].reservationExpiry = expiry.toISOString()
    codes[codeIndex].reservedAt = new Date().toISOString()
    
    localStorage.setItem(codesKey, JSON.stringify(codes))
  }
}

function confirmDigitalCodeAssignment(
  productId: string,
  packageId: string,
  codeId: string,
  orderId: string
): boolean {
  const codesKey = `digital_codes_${packageId}`
  const codes = JSON.parse(localStorage.getItem(codesKey) || '[]')
  
  const codeIndex = codes.findIndex((code: any) => 
    code.id === codeId && 
    code.reservedForOrderId === orderId
  )
  
  if (codeIndex >= 0) {
    codes[codeIndex].isUsed = true
    codes[codeIndex].isReserved = false
    codes[codeIndex].assignedToOrderId = orderId
    codes[codeIndex].usedAt = new Date().toISOString()
    delete codes[codeIndex].reservationExpiry
    delete codes[codeIndex].reservedForOrderId
    
    localStorage.setItem(codesKey, JSON.stringify(codes))
    return true
  }
  
  return false
}

function releaseReservationIfNotUsed(
  productId: string,
  packageId: string,
  codeId: string,
  orderId: string
): void {
  const codesKey = `digital_codes_${packageId}`
  const codes = JSON.parse(localStorage.getItem(codesKey) || '[]')
  
  const codeIndex = codes.findIndex((code: any) => 
    code.id === codeId && 
    code.reservedForOrderId === orderId &&
    !code.isUsed
  )
  
  if (codeIndex >= 0) {
    codes[codeIndex].isReserved = false
    delete codes[codeIndex].reservedForOrderId
    delete codes[codeIndex].reservationExpiry
    delete codes[codeIndex].reservedAt
    
    localStorage.setItem(codesKey, JSON.stringify(codes))
    
    console.log(`Released expired reservation for code ${codeId}`)
  }
}

function generateTransactionId(): string {
  return `txn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}
