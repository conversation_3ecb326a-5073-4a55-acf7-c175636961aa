/**
 * Upload Service
 * 
 * Provides flexible image upload functionality with multiple backend options:
 * - Base64 encoding (default, no external dependencies)
 * - File upload to custom endpoint
 * - External services (ImgBB, Cloudinary, etc.)
 */

export type UploadMode = 'base64' | 'file' | 'url'

export interface UploadService {
  upload: (file: File) => Promise<string>
  mode: UploadMode
  name: string
}

// Base64 Upload Service (Default)
export const base64UploadService: UploadService = {
  name: 'Base64',
  mode: 'base64',
  upload: async (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onloadend = () => {
        const result = reader.result as string
        resolve(result)
      }
      reader.onerror = () => reject(new Error('Failed to read file'))
    })
  }
}

// File Upload Service (for custom endpoints)
export const createFileUploadService = (endpoint: string, options?: {
  headers?: Record<string, string>
  fieldName?: string
}): UploadService => ({
  name: 'File Upload',
  mode: 'file',
  upload: async (file: File): Promise<string> => {
    const formData = new FormData()
    formData.append(options?.fieldName || 'file', file)

    const response = await fetch(endpoint, {
      method: 'POST',
      headers: options?.headers || {},
      body: formData
    })

    if (!response.ok) {
      throw new Error(`Upload failed: ${response.statusText}`)
    }

    const data = await response.json()
    
    // Assume the response contains a 'url' field
    // Adjust this based on your API response structure
    if (data.url) {
      return data.url
    } else if (data.data?.url) {
      return data.data.url
    } else {
      throw new Error('No URL returned from upload service')
    }
  }
})

// ImgBB Upload Service
export const createImgBBUploadService = (apiKey: string): UploadService => ({
  name: 'ImgBB',
  mode: 'url',
  upload: async (file: File): Promise<string> => {
    // Validate file size (ImgBB limit: 32MB, but we'll use 10MB for better UX)
    const maxSize = 10 * 1024 * 1024 // 10MB
    if (file.size > maxSize) {
      throw new Error('حجم الملف كبير جداً. الحد الأقصى 10 ميجابايت')
    }
    
    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      throw new Error('نوع الملف غير مدعوم. الأنواع المدعومة: JPG, PNG, GIF, WEBP')
    }

    const formData = new FormData()
    formData.append('key', apiKey)
    formData.append('image', file)
    formData.append('expiration', '0') // Never expire
    formData.append('format', 'json')

    const response = await fetch('https://api.imgbb.com/1/upload', {
      method: 'POST',
      body: formData
    })

    if (!response.ok) {
      throw new Error('فشل في رفع الصورة إلى ImgBB')
    }

    const data = await response.json()
    
    if (data.success && data.data?.image?.url) {
      return data.data.image.url
    } else {
      throw new Error('فشل في رفع الصورة')
    }
  }
})

// Cloudinary Upload Service
export const createCloudinaryUploadService = (
  cloudName: string, 
  uploadPreset: string
): UploadService => ({
  name: 'Cloudinary',
  mode: 'url',
  upload: async (file: File): Promise<string> => {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('upload_preset', uploadPreset)

    const response = await fetch(
      `https://api.cloudinary.com/v1_1/${cloudName}/image/upload`,
      {
        method: 'POST',
        body: formData
      }
    )

    if (!response.ok) {
      throw new Error('فشل في رفع الصورة إلى Cloudinary')
    }

    const data = await response.json()
    
    if (data.secure_url) {
      return data.secure_url
    } else {
      throw new Error('فشل في رفع الصورة')
    }
  }
})

// Supabase Storage Upload Service
export const createSupabaseUploadService = (
  supabaseUrl: string,
  supabaseKey: string,
  bucket: string
): UploadService => ({
  name: 'Supabase Storage',
  mode: 'file',
  upload: async (file: File): Promise<string> => {
    const fileName = `${Date.now()}-${file.name}`
    
    const response = await fetch(
      `${supabaseUrl}/storage/v1/object/${bucket}/${fileName}`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${supabaseKey}`,
          'Content-Type': file.type,
        },
        body: file
      }
    )

    if (!response.ok) {
      throw new Error('فشل في رفع الصورة إلى Supabase')
    }

    // Return the public URL
    return `${supabaseUrl}/storage/v1/object/public/${bucket}/${fileName}`
  }
})

// Utility function to validate image files
export const validateImageFile = (
  file: File, 
  maxSizeMB: number = 10,
  allowedTypes: string[] = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
): { valid: boolean; error?: string } => {
  // Check file type
  if (!allowedTypes.includes(file.type)) {
    return {
      valid: false,
      error: `نوع الملف غير مدعوم. الأنواع المدعومة: ${allowedTypes.join(', ')}`
    }
  }

  // Check file size
  const maxSizeBytes = maxSizeMB * 1024 * 1024
  if (file.size > maxSizeBytes) {
    return {
      valid: false,
      error: `حجم الملف كبير جداً. الحد الأقصى ${maxSizeMB} ميجابايت`
    }
  }

  return { valid: true }
}

// Utility function to test if an image URL is valid
export const testImageUrl = (url: string): Promise<boolean> => {
  return new Promise((resolve) => {
    const img = new Image()
    img.onload = () => resolve(true)
    img.onerror = () => resolve(false)
    img.src = url
  })
}

// Default export
export default base64UploadService
