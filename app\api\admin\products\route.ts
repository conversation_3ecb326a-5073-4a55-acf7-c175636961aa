/**
 * Secure Admin Products API Route
 * Demonstrates proper authentication, validation, and error handling
 */

import { NextRequest, NextResponse } from 'next/server'
import { withAdminAuth, withRateLimit, withValidation, withLogging, combineMiddlewares } from '@/lib/middleware/auth'
import { validateProductName, validateProductDescription, BatchValidationResult } from '@/lib/utils/validation'
import { OptimizedProductStorage } from '@/lib/utils/optimizedStorage'
import { ProductTemplate } from '@/lib/types'

/**
 * Validation schema for product creation
 */
function validateProductData(data: any): { isValid: boolean; errors?: string[]; data?: Partial<ProductTemplate> } {
  const errors: string[] = []
  const sanitizedData: any = {}

  // Validate name
  const nameValidation = validateProductName(data.name)
  if (!nameValidation.isValid) {
    errors.push(nameValidation.error!)
  } else {
    sanitizedData.name = nameValidation.sanitizedValue || data.name
  }

  // Validate description
  const descValidation = validateProductDescription(data.description)
  if (!descValidation.isValid) {
    errors.push(descValidation.error!)
  } else {
    sanitizedData.description = descValidation.sanitizedValue || data.description
  }

  // Validate category
  if (!data.category || typeof data.category !== 'string') {
    errors.push('Category is required')
  } else {
    sanitizedData.category = data.category.trim()
  }

  // Validate packages
  if (!Array.isArray(data.packages) || data.packages.length === 0) {
    errors.push('At least one package is required')
  } else {
    sanitizedData.packages = data.packages
  }

  return {
    isValid: errors.length === 0,
    errors: errors.length > 0 ? errors : undefined,
    data: sanitizedData
  }
}

/**
 * GET /api/admin/products
 * Get all products with pagination and filtering
 */
async function getProducts(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const category = searchParams.get('category')
    const search = searchParams.get('search')
    const isActive = searchParams.get('isActive')

    // Validate pagination parameters
    if (page < 1 || limit < 1 || limit > 100) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid pagination parameters' 
        },
        { status: 400 }
      )
    }

    const result = OptimizedProductStorage.search(search || '', {
      page,
      limit,
      category: category || undefined,
      isActive: isActive ? isActive === 'true' : undefined
    })

    return NextResponse.json({
      success: true,
      data: {
        products: result.products,
        pagination: {
          page,
          limit,
          total: result.total,
          hasMore: result.hasMore,
          totalPages: Math.ceil(result.total / limit)
        }
      }
    })

  } catch (error) {
    console.error('Error fetching products:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to fetch products' 
      },
      { status: 500 }
    )
  }
}

/**
 * POST /api/admin/products
 * Create a new product
 */
async function createProduct(request: NextRequest, context: any, validatedData: Partial<ProductTemplate>) {
  try {
    // Additional business logic validation
    const existingProducts = OptimizedProductStorage.getAll()
    const nameExists = existingProducts.some(p => 
      p.name.toLowerCase() === validatedData.name?.toLowerCase() && p.isActive
    )

    if (nameExists) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Product with this name already exists' 
        },
        { status: 409 }
      )
    }

    // Create product with default values
    const productData = {
      ...validatedData,
      isActive: true,
      isFeatured: false,
      tags: validatedData.tags || [],
      fields: validatedData.fields || [],
      packages: validatedData.packages || [],
      deliveryMethod: validatedData.deliveryMethod || 'manual',
      processingType: validatedData.processingType || 'manual'
    }

    const newProduct = OptimizedProductStorage.create(productData as Omit<ProductTemplate, 'id' | 'createdAt' | 'updatedAt'>)

    // Log admin action
    console.log(`Admin ${context.user.email} created product: ${newProduct.name} (ID: ${newProduct.id})`)

    return NextResponse.json({
      success: true,
      data: {
        product: newProduct,
        message: 'Product created successfully'
      }
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating product:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to create product' 
      },
      { status: 500 }
    )
  }
}

/**
 * PUT /api/admin/products/[id]
 * Update an existing product
 */
async function updateProduct(request: NextRequest, context: any, validatedData: Partial<ProductTemplate>) {
  try {
    const url = new URL(request.url)
    const productId = url.pathname.split('/').pop()

    if (!productId) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Product ID is required' 
        },
        { status: 400 }
      )
    }

    const existingProduct = OptimizedProductStorage.getById(productId)
    if (!existingProduct) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Product not found' 
        },
        { status: 404 }
      )
    }

    // Check for name conflicts (excluding current product)
    if (validatedData.name) {
      const allProducts = OptimizedProductStorage.getAll()
      const nameExists = allProducts.some(p => 
        p.id !== productId &&
        p.name.toLowerCase() === validatedData.name?.toLowerCase() && 
        p.isActive
      )

      if (nameExists) {
        return NextResponse.json(
          { 
            success: false,
            error: 'Product with this name already exists' 
          },
          { status: 409 }
        )
      }
    }

    const updatedProduct = OptimizedProductStorage.update(productId, validatedData)

    if (!updatedProduct) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Failed to update product' 
        },
        { status: 500 }
      )
    }

    // Log admin action
    console.log(`Admin ${context.user.email} updated product: ${updatedProduct.name} (ID: ${productId})`)

    return NextResponse.json({
      success: true,
      data: {
        product: updatedProduct,
        message: 'Product updated successfully'
      }
    })

  } catch (error) {
    console.error('Error updating product:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to update product' 
      },
      { status: 500 }
    )
  }
}

/**
 * DELETE /api/admin/products/[id]
 * Soft delete a product
 */
async function deleteProduct(request: NextRequest, context: any) {
  try {
    const url = new URL(request.url)
    const productId = url.pathname.split('/').pop()

    if (!productId) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Product ID is required' 
        },
        { status: 400 }
      )
    }

    const existingProduct = OptimizedProductStorage.getById(productId)
    if (!existingProduct) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Product not found' 
        },
        { status: 404 }
      )
    }

    // Soft delete by setting isActive to false
    const updatedProduct = OptimizedProductStorage.update(productId, { isActive: false })

    if (!updatedProduct) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Failed to delete product' 
        },
        { status: 500 }
      )
    }

    // Log admin action
    console.log(`Admin ${context.user.email} deleted product: ${existingProduct.name} (ID: ${productId})`)

    return NextResponse.json({
      success: true,
      data: {
        message: 'Product deleted successfully'
      }
    })

  } catch (error) {
    console.error('Error deleting product:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to delete product' 
      },
      { status: 500 }
    )
  }
}

// Apply middleware stack
const secureHandler = combineMiddlewares(
  withLogging,
  withRateLimit(100, 60000), // 100 requests per minute
  withAdminAuth
)

// Route handlers with security
export const GET = secureHandler(getProducts)
export const POST = secureHandler(withValidation(validateProductData, createProduct))
export const PUT = secureHandler(withValidation(validateProductData, updateProduct))
export const DELETE = secureHandler(deleteProduct)
