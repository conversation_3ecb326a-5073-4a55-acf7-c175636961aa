/**
 * User Preferences Utility
 * Manages user preferences including currency settings
 */

import { Currency } from '@/lib/types'

export interface UserPreferences {
  userId: string
  preferredCurrency: Currency
  displayCurrency: Currency
  enableCurrencyConversion: boolean
  conversionConfirmationRequired: boolean
  language: 'ar' | 'en'
  theme: 'light' | 'dark' | 'auto'
  notifications: {
    email: boolean
    push: boolean
    sms: boolean
  }
  privacy: {
    shareAnalytics: boolean
    marketingEmails: boolean
  }
}

const DEFAULT_PREFERENCES: Omit<UserPreferences, 'userId'> = {
  preferredCurrency: 'USD',
  displayCurrency: 'USD',
  enableCurrencyConversion: true,
  conversionConfirmationRequired: true,
  language: 'ar',
  theme: 'auto',
  notifications: {
    email: true,
    push: true,
    sms: false
  },
  privacy: {
    shareAnalytics: false,
    marketingEmails: false
  }
}

/**
 * Get user preferences from localStorage
 */
export function getUserPreferences(userId: string): UserPreferences {
  try {
    const stored = localStorage.getItem(`user_preferences_${userId}`)
    if (stored) {
      const parsed = JSON.parse(stored)
      return {
        ...DEFAULT_PREFERENCES,
        ...parsed,
        userId
      }
    }
  } catch (error) {
    console.error('Error loading user preferences:', error)
  }

  return {
    ...DEFAULT_PREFERENCES,
    userId
  }
}

/**
 * Save user preferences to localStorage
 */
export function saveUserPreferences(preferences: UserPreferences): void {
  try {
    localStorage.setItem(
      `user_preferences_${preferences.userId}`,
      JSON.stringify(preferences)
    )
    
    // Dispatch event for real-time updates
    window.dispatchEvent(new CustomEvent('userPreferencesUpdated', {
      detail: { preferences }
    }))
  } catch (error) {
    console.error('Error saving user preferences:', error)
    throw new Error('Failed to save preferences')
  }
}

/**
 * Get user's preferred currency
 */
export function getUserPreferredCurrency(userId: string): Currency {
  const preferences = getUserPreferences(userId)
  return preferences.preferredCurrency
}

/**
 * Get user's display currency
 */
export function getUserDisplayCurrency(userId: string): Currency {
  const preferences = getUserPreferences(userId)
  return preferences.displayCurrency
}

/**
 * Update user's currency preference
 */
export function updateUserCurrencyPreference(
  userId: string,
  preferredCurrency: Currency,
  displayCurrency?: Currency
): void {
  const preferences = getUserPreferences(userId)
  const updated = {
    ...preferences,
    preferredCurrency,
    displayCurrency: displayCurrency || preferredCurrency
  }
  saveUserPreferences(updated)
}

/**
 * Check if user allows currency conversion
 */
export function isConversionEnabled(userId: string): boolean {
  const preferences = getUserPreferences(userId)
  return preferences.enableCurrencyConversion
}

/**
 * Check if user requires confirmation for conversions
 */
export function requiresConversionConfirmation(userId: string): boolean {
  const preferences = getUserPreferences(userId)
  return preferences.conversionConfirmationRequired
}

/**
 * Get user's language preference
 */
export function getUserLanguage(userId: string): 'ar' | 'en' {
  const preferences = getUserPreferences(userId)
  return preferences.language
}

/**
 * Update user's language preference
 */
export function updateUserLanguage(userId: string, language: 'ar' | 'en'): void {
  const preferences = getUserPreferences(userId)
  saveUserPreferences({
    ...preferences,
    language
  })
}

/**
 * Get user's theme preference
 */
export function getUserTheme(userId: string): 'light' | 'dark' | 'auto' {
  const preferences = getUserPreferences(userId)
  return preferences.theme
}

/**
 * Update user's theme preference
 */
export function updateUserTheme(userId: string, theme: 'light' | 'dark' | 'auto'): void {
  const preferences = getUserPreferences(userId)
  saveUserPreferences({
    ...preferences,
    theme
  })
}

/**
 * Get notification preferences
 */
export function getNotificationPreferences(userId: string) {
  const preferences = getUserPreferences(userId)
  return preferences.notifications
}

/**
 * Update notification preferences
 */
export function updateNotificationPreferences(
  userId: string,
  notifications: Partial<UserPreferences['notifications']>
): void {
  const preferences = getUserPreferences(userId)
  saveUserPreferences({
    ...preferences,
    notifications: {
      ...preferences.notifications,
      ...notifications
    }
  })
}

/**
 * Get privacy preferences
 */
export function getPrivacyPreferences(userId: string) {
  const preferences = getUserPreferences(userId)
  return preferences.privacy
}

/**
 * Update privacy preferences
 */
export function updatePrivacyPreferences(
  userId: string,
  privacy: Partial<UserPreferences['privacy']>
): void {
  const preferences = getUserPreferences(userId)
  saveUserPreferences({
    ...preferences,
    privacy: {
      ...preferences.privacy,
      ...privacy
    }
  })
}

/**
 * Reset user preferences to defaults
 */
export function resetUserPreferences(userId: string): void {
  const defaultPrefs: UserPreferences = {
    ...DEFAULT_PREFERENCES,
    userId
  }
  saveUserPreferences(defaultPrefs)
}

/**
 * Export user preferences for backup
 */
export function exportUserPreferences(userId: string): string {
  const preferences = getUserPreferences(userId)
  return JSON.stringify(preferences, null, 2)
}

/**
 * Import user preferences from backup
 */
export function importUserPreferences(userId: string, preferencesJson: string): void {
  try {
    const preferences = JSON.parse(preferencesJson)
    if (preferences.userId !== userId) {
      throw new Error('User ID mismatch')
    }
    saveUserPreferences(preferences)
  } catch (error) {
    console.error('Error importing preferences:', error)
    throw new Error('Invalid preferences format')
  }
}

/**
 * Get default currency for anonymous users
 */
export function getDefaultCurrency(): Currency {
  // Try to detect user's location and return appropriate currency
  try {
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone
    
    if (timezone.includes('Cairo') || timezone.includes('Africa/Cairo')) {
      return 'EGP'
    }
    
    if (timezone.includes('Khartoum') || timezone.includes('Africa/Khartoum')) {
      return 'SDG'
    }
    
    // Default to USD for other locations
    return 'USD'
  } catch {
    return 'USD'
  }
}

/**
 * Get currency for user (with fallback for anonymous users)
 */
export function getCurrencyForUser(userId?: string): Currency {
  if (!userId) {
    return getDefaultCurrency()
  }
  
  return getUserPreferredCurrency(userId)
}

/**
 * Validate user preferences object
 */
export function validateUserPreferences(preferences: any): boolean {
  if (!preferences || typeof preferences !== 'object') {
    return false
  }
  
  const required = ['userId', 'preferredCurrency', 'displayCurrency']
  for (const field of required) {
    if (!(field in preferences)) {
      return false
    }
  }
  
  const validCurrencies: Currency[] = ['USD', 'SDG', 'EGP']
  if (!validCurrencies.includes(preferences.preferredCurrency) ||
      !validCurrencies.includes(preferences.displayCurrency)) {
    return false
  }
  
  return true
}

/**
 * Migrate old preferences format to new format
 */
export function migrateUserPreferences(userId: string): void {
  try {
    // Check for old format preferences
    const oldPrefs = localStorage.getItem(`preferences_${userId}`)
    if (oldPrefs) {
      const parsed = JSON.parse(oldPrefs)
      
      // Convert to new format
      const newPrefs: UserPreferences = {
        ...DEFAULT_PREFERENCES,
        userId,
        preferredCurrency: parsed.currency || 'USD',
        displayCurrency: parsed.currency || 'USD',
        language: parsed.language || 'ar'
      }
      
      saveUserPreferences(newPrefs)
      
      // Remove old format
      localStorage.removeItem(`preferences_${userId}`)
      
      console.log('Migrated user preferences to new format')
    }
  } catch (error) {
    console.error('Error migrating preferences:', error)
  }
}
